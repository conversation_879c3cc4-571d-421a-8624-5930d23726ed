const ScreenAdapter = require('../utils/ScreenAdapter.js')
const BackButton = require('../components/BackButton.js')
const AudioManager = require('../utils/AudioManager.js')
const DataManager = require('../utils/DataManager.js')

/**
 * 关卡战斗场景
 */
class BattleScene {
  constructor(canvas, ctx, levelId = 1) {
    this.canvas = canvas
    this.ctx = ctx
    this.adapter = new ScreenAdapter()
    
    // 创建返回按钮
    this.backButtonComponent = new BackButton(require('../utils/ImageManager.js'))
    
    // 关卡信息
    this.levelId = levelId
    this.levelName = `关卡 ${levelId}`
    
    // 游戏状态
    this.gameState = 'playing' // preparing, playing, paused, victory, defeat
    this.isPaused = false
    this.gameSpeed = 1 // 1x, 2x
    
    // 游戏数据
    this.gold = 200
    this.lives = 20
    this.score = 0
    this.currentWave = 0
    this.totalWaves = 5
    
    // 地图配置
    this.mapWidth = this.adapter.getWindowWidth()
    this.mapHeight = this.adapter.getWindowHeight()

    // 网格系统
    this.gridSize = 50  // 网格大小50x50像素
    this.gridCols = Math.floor(this.mapWidth / this.gridSize)
    this.gridRows = Math.floor(this.mapHeight / this.gridSize)
    this.showGrid = false  // 是否显示网格
    
    // 路径配置（简单的L型路径）
    this.path = this.createPath()
    
    // 游戏实体
    this.towers = []
    this.enemies = []
    this.bullets = []
    this.effects = []
    
    // UI配置
    this.setupUI()
    
    // 波次管理
    this.waveStartTime = 0
    this.enemySpawnQueue = []
    this.lastEnemySpawnTime = 0
    this.enemySpawnInterval = 2000 // 2秒生成一个敌人
    this.enemiesSpawned = 0
    this.maxEnemiesPerWave = 10
    
    // 建造模式
    this.buildMode = false
    this.selectedTowerType = null  // 初始时不选择任何塔类型
    this.buildPreviewPos = null

    // 选中的塔
    this.selectedTower = null
    
    // 动画和效果
    this.animationTime = 0
    this.particles = []
    
    // 绑定事件
    this.bindEvents()
    
    // 播放背景音乐
    this.playBackgroundMusic()

    // 开始游戏
    this.startGame()

    console.log(`战斗场景初始化完成 - 关卡${levelId}`)
  }
  
  /**
   * 创建路径
   */
  createPath() {
    const centerY = this.mapHeight / 2
    const quarterY = this.mapHeight / 4
    const threeQuarterY = this.mapHeight * 3 / 4

    return [
      { x: 0, y: centerY },                    // 起点：左侧中央
      { x: this.mapWidth * 0.25, y: centerY }, // 第一段：向右
      { x: this.mapWidth * 0.25, y: quarterY }, // 转向上
      { x: this.mapWidth * 0.75, y: quarterY }, // 向右上
      { x: this.mapWidth * 0.75, y: threeQuarterY }, // 向下
      { x: this.mapWidth, y: threeQuarterY }   // 终点：右侧下方
    ]
  }
  
  /**
   * 设置UI
   */
  setupUI() {
    const windowWidth = this.adapter.getWindowWidth()
    const windowHeight = this.adapter.getWindowHeight()
    
    // 游戏信息面板（顶部居中一行显示）
    const panelWidth = 100
    const panelHeight = 30
    const spacing = 5
    const totalWidth = panelWidth * 4 + spacing * 3 // 4个面板 + 3个间距
    const startX = (windowWidth - totalWidth) / 2

    this.gameHUD = {
      gold: {
        x: startX,
        y: 10,
        width: panelWidth,
        height: panelHeight,
        text: `金币: ${this.gold}`
      },
      lives: {
        x: startX + panelWidth + spacing,
        y: 10,
        width: panelWidth,
        height: panelHeight,
        text: `生命: ${this.lives}`
      },
      score: {
        x: startX + (panelWidth + spacing) * 2,
        y: 10,
        width: panelWidth,
        height: panelHeight,
        text: `分数: ${this.score}`
      },
      wave: {
        x: startX + (panelWidth + spacing) * 3,
        y: 10,
        width: panelWidth,
        height: panelHeight,
        text: `波次: ${this.currentWave}/${this.totalWaves}`
      }
    }
    
    // 控制按钮（右侧中间）
    this.controlButtons = {
      pause: {
        x: windowWidth - 60,
        y: windowHeight / 2 - 40,
        width: 50,
        height: 35,
        text: '暂停',
        action: 'pause'
      },
      speed: {
        x: windowWidth - 60,
        y: windowHeight / 2 + 5,
        width: 50,
        height: 35,
        text: '1x',
        action: 'speed'
      }
    }
    
    // 建造按钮面板状态
    this.buildPanelExpanded = false

    // 建造按钮（从右往左展开，魔法塔最靠近胶囊）
    this.buildButtons = {
      magic: {
        x: windowWidth - 120,  // 紧贴胶囊按钮
        y: windowHeight - 60,
        width: 50,
        height: 50,
        text: '魔法塔',
        cost: 150,
        type: 'magic',
        visible: false
      },
      cannon: {
        x: windowWidth - 180,  // 中间位置
        y: windowHeight - 60,
        width: 50,
        height: 50,
        text: '炮塔',
        cost: 100,
        type: 'cannon',
        visible: false
      },
      basic: {
        x: windowWidth - 240,  // 最左边
        y: windowHeight - 60,
        width: 50,
        height: 50,
        text: '基础塔',
        cost: 50,
        type: 'basic',
        visible: false
      }
    }

    // 胶囊按钮（控制建造面板的展开/收起）
    this.capsuleButton = {
      x: windowWidth - 60,
      y: windowHeight - 60,
      width: 50,
      height: 50,
      text: '建造',
      expanded: false
    }
    

  }
  
  /**
   * 播放背景音乐
   */
  playBackgroundMusic() {
    const settings = DataManager.getSettings()
    if (settings.musicEnabled) {
      AudioManager.playMusic('battle_theme', {
        volume: settings.musicVolume,
        loop: true
      })
    }
  }

  /**
   * 开始游戏
   */
  startGame() {
    this.gameState = 'playing'
    this.currentWave = 1
    this.lastEnemySpawnTime = Date.now()

    // 立即生成第一个敌人
    this.spawnEnemy()

    console.log('游戏开始！')
  }
  
  /**
   * 绑定事件
   */
  bindEvents() {
    this.touchStartHandler = this.onTouchStart.bind(this)
    this.touchEndHandler = this.onTouchEnd.bind(this)
    this.touchMoveHandler = this.onTouchMove.bind(this)

    wx.onTouchStart(this.touchStartHandler)
    wx.onTouchEnd(this.touchEndHandler)
    wx.onTouchMove(this.touchMoveHandler)
  }
  
  /**
   * 触摸开始事件
   */
  onTouchStart(e) {
    const touch = e.touches[0]
    const pos = this.adapter.convertTouchToCanvas(touch)
    
    // 检查返回按钮
    if (this.backButtonComponent.isPointInButton(pos)) {
      this.backButtonComponent.setPressed(true)
      return
    }
    
    // 检查控制按钮
    for (let buttonId in this.controlButtons) {
      const button = this.controlButtons[buttonId]
      if (this.isPointInRect(pos, button)) {
        this.handleControlButton(buttonId)
        return
      }
    }
    
    // 检查胶囊按钮
    if (this.isPointInRect(pos, this.capsuleButton)) {
      this.toggleBuildPanel()
      return
    }

    // 检查建造按钮（只有在展开状态下才检测）
    if (this.buildPanelExpanded) {
      for (let buttonId in this.buildButtons) {
        const button = this.buildButtons[buttonId]
        if (button.visible && this.isPointInRect(pos, button)) {
          this.handleBuildButton(buttonId)
          return
        }
      }
    }
    
    // 检查地图点击
    this.handleMapClick(pos)
  }
  
  /**
   * 触摸结束事件
   */
  onTouchEnd(e) {
    const touch = e.changedTouches[0]
    const pos = this.adapter.convertTouchToCanvas(touch)

    // 检查返回按钮
    if (this.backButtonComponent.isPointInButton(pos)) {
      this.backButtonComponent.setPressed(false)
      // 返回冒险地图
      if (this.sceneChangeCallback) {
        this.sceneChangeCallback('adventure-map')
      }
      return
    }

    this.backButtonComponent.setPressed(false)
  }

  /**
   * 触摸移动事件
   */
  onTouchMove(e) {
    if (!this.buildMode) return

    const touch = e.touches[0]
    const pos = this.adapter.convertTouchToCanvas(touch)

    // 更新建造预览位置
    this.buildPreviewPos = pos
  }
  
  /**
   * 检查点是否在矩形内
   */
  isPointInRect(point, rect) {
    return point.x >= rect.x && point.x <= rect.x + rect.width &&
           point.y >= rect.y && point.y <= rect.y + rect.height
  }
  
  /**
   * 处理控制按钮
   */
  handleControlButton(buttonId) {
    switch (buttonId) {
      case 'pause':
        this.togglePause()
        break
      case 'speed':
        this.toggleSpeed()
        break
    }
  }
  
  /**
   * 切换建造面板
   */
  toggleBuildPanel() {
    this.buildPanelExpanded = !this.buildPanelExpanded

    if (this.buildPanelExpanded) {
      // 展开：从右往左依次显示（魔法塔 → 炮塔 → 基础塔）
      setTimeout(() => { this.buildButtons.magic.visible = true }, 0)
      setTimeout(() => { this.buildButtons.cannon.visible = true }, 100)
      setTimeout(() => { this.buildButtons.basic.visible = true }, 200)
    } else {
      // 收起：从左往右依次隐藏（基础塔 → 炮塔 → 魔法塔）
      this.buildButtons.basic.visible = false
      setTimeout(() => { this.buildButtons.cannon.visible = false }, 50)
      setTimeout(() => { this.buildButtons.magic.visible = false }, 100)
    }

    // 更新胶囊按钮文字
    this.capsuleButton.text = this.buildPanelExpanded ? '收起' : '建造'
    this.capsuleButton.expanded = this.buildPanelExpanded

    console.log(this.buildPanelExpanded ? '建造面板展开' : '建造面板收起')
  }

  /**
   * 处理建造按钮
   */
  handleBuildButton(buttonId) {
    const button = this.buildButtons[buttonId]

    if (this.buildMode && this.selectedTowerType === button.type) {
      // 如果点击的是已选中的建造按钮，退出建造模式
      this.buildMode = false
      this.selectedTowerType = null
      console.log('退出建造模式')
    } else if (this.gold >= button.cost) {
      // 选择新的建造类型
      this.selectedTowerType = button.type
      this.buildMode = true
      console.log(`选择建造: ${button.text}`)
    } else {
      console.log('金币不足')
    }
  }
  
  /**
   * 处理地图点击
   */
  handleMapClick(pos) {
    if (this.buildMode) {
      this.tryBuildTower(pos.x, pos.y)
    } else {
      this.selectTower(pos.x, pos.y)
    }
  }
  
  /**
   * 切换暂停状态
   */
  togglePause() {
    this.isPaused = !this.isPaused
    this.controlButtons.pause.text = this.isPaused ? '继续' : '暂停'
    console.log(this.isPaused ? '游戏暂停' : '游戏继续')
  }
  
  /**
   * 切换游戏速度
   */
  toggleSpeed() {
    this.gameSpeed = this.gameSpeed === 1 ? 2 : 1
    this.controlButtons.speed.text = `${this.gameSpeed}x`
    console.log(`游戏速度: ${this.gameSpeed}x`)
  }
  
  /**
   * 尝试建造塔
   */
  tryBuildTower(x, y) {
    // 将点击位置对齐到网格
    const gridPos = this.snapToGrid(x, y)

    // 检查是否可以在此位置建造
    if (!this.canBuildAt(gridPos.x, gridPos.y)) {
      console.log('此位置无法建造')
      return false
    }

    // 获取建造费用
    const button = this.buildButtons[this.selectedTowerType]
    if (this.gold < button.cost) {
      console.log('金币不足')
      return false
    }

    // 建造塔
    this.buildTower(gridPos.x, gridPos.y, this.selectedTowerType, button.cost)
    return true
  }

  /**
   * 将坐标对齐到网格中心
   */
  snapToGrid(x, y) {
    const gridX = Math.floor(x / this.gridSize)
    const gridY = Math.floor(y / this.gridSize)

    return {
      x: gridX * this.gridSize + this.gridSize / 2,
      y: gridY * this.gridSize + this.gridSize / 2
    }
  }

  /**
   * 检查是否可以在指定位置建造
   */
  canBuildAt(x, y) {
    // 检查是否在路径上
    if (this.isOnPath(x, y)) {
      return false
    }

    // 检查是否与其他塔重叠
    if (this.isPositionOccupied(x, y)) {
      return false
    }

    // 检查是否在地图边界内
    if (x < this.gridSize/2 || x > this.mapWidth - this.gridSize/2 ||
        y < this.gridSize/2 || y > this.mapHeight - this.gridSize/2) {
      return false
    }

    return true
  }
  
  /**
   * 检查位置是否在路径上
   */
  isOnPath(x, y) {
    const pathWidth = 40

    for (let i = 0; i < this.path.length - 1; i++) {
      const p1 = this.path[i]
      const p2 = this.path[i + 1]

      const distance = this.distanceToLineSegment(x, y, p1.x, p1.y, p2.x, p2.y)
      if (distance < pathWidth) {
        return true
      }
    }

    return false
  }
  
  /**
   * 计算点到线段的距离
   */
  distanceToLineSegment(px, py, x1, y1, x2, y2) {
    const dx = x2 - x1
    const dy = y2 - y1
    const length = Math.sqrt(dx * dx + dy * dy)
    
    if (length === 0) {
      return Math.sqrt((px - x1) * (px - x1) + (py - y1) * (py - y1))
    }
    
    const t = Math.max(0, Math.min(1, ((px - x1) * dx + (py - y1) * dy) / (length * length)))
    const projX = x1 + t * dx
    const projY = y1 + t * dy
    
    return Math.sqrt((px - projX) * (px - projX) + (py - projY) * (py - projY))
  }
  
  /**
   * 检查位置是否被占用
   */
  isPositionOccupied(x, y) {
    // 使用网格系统，检查是否有塔在同一网格位置
    for (const tower of this.towers) {
      const distance = Math.sqrt((tower.x - x) * (tower.x - x) + (tower.y - y) * (tower.y - y))
      if (distance < this.gridSize / 2) {
        return true
      }
    }

    return false
  }
  
  /**
   * 建造塔
   */
  buildTower(x, y, type, cost) {
    const tower = {
      id: Date.now(),
      x: x,
      y: y,
      type: type,
      level: 1,
      damage: this.getTowerDamage(type),
      range: this.getTowerRange(type),
      fireRate: this.getTowerFireRate(type),
      lastFireTime: 0,
      target: null
    }
    
    this.towers.push(tower)
    this.gold -= cost
    // 保持建造模式和选中状态，方便连续建造同类型塔
    // this.buildMode = false
    // this.selectedTowerType = null

    console.log(`建造了${type}塔，剩余金币: ${this.gold}，可继续建造`)
  }
  
  /**
   * 获取塔的伤害
   */
  getTowerDamage(type) {
    const damages = {
      basic: 20,
      cannon: 50,
      magic: 30
    }
    return damages[type] || 20
  }
  
  /**
   * 获取塔的射程
   */
  getTowerRange(type) {
    const ranges = {
      basic: 80,
      cannon: 100,
      magic: 120
    }
    return ranges[type] || 80
  }
  
  /**
   * 获取塔的射速
   */
  getTowerFireRate(type) {
    const fireRates = {
      basic: 1000, // 1秒
      cannon: 2000, // 2秒
      magic: 1500  // 1.5秒
    }
    return fireRates[type] || 1000
  }
  


  /**
   * 选择塔
   */
  selectTower(x, y) {
    let clickedTower = null

    // 查找点击的塔（使用网格范围）
    for (const tower of this.towers) {
      const distance = Math.sqrt((tower.x - x) * (tower.x - x) + (tower.y - y) * (tower.y - y))
      if (distance < this.gridSize / 2) {
        clickedTower = tower
        break
      }
    }

    if (clickedTower) {
      if (this.selectedTower === clickedTower) {
        // 如果点击的是已选中的塔，取消选择
        this.selectedTower = null
        console.log('取消选择塔')
      } else {
        // 选择新的塔
        this.selectedTower = clickedTower
        console.log(`选中了${clickedTower.type}塔 - 伤害:${clickedTower.damage} 射程:${clickedTower.range}`)
      }
    } else {
      // 点击空地，取消选择
      this.selectedTower = null
    }
  }
  
  /**
   * 渲染场景
   */
  render() {
    // 应用像素比缩放（与其他场景保持一致）
    this.ctx.save()
    this.ctx.scale(this.adapter.getPixelRatio(), this.adapter.getPixelRatio())

    // 绘制背景
    this.drawBackground()

    // 绘制路径
    this.drawPath()

    // 绘制网格（在建造模式下）
    this.drawGrid()

    // 绘制塔
    this.drawTowers()

    // 绘制建造预览
    this.drawBuildPreview()

    // 绘制敌人
    this.drawEnemies()

    // 绘制子弹
    this.drawBullets()

    // 绘制UI
    this.drawUI()

    // 绘制返回按钮
    this.backButtonComponent.render(this.ctx)

    // 绘制游戏状态提示
    this.drawGameStatus()

    this.ctx.restore()
  }

  /**
   * 绘制背景
   */
  drawBackground() {
    // 绘制草地背景
    this.ctx.fillStyle = '#90EE90'
    this.ctx.fillRect(0, 0, this.mapWidth, this.mapHeight)

    // 添加一些纹理效果
    this.ctx.fillStyle = 'rgba(34, 139, 34, 0.1)'
    for (let i = 0; i < 50; i++) {
      const x = Math.random() * this.mapWidth
      const y = Math.random() * this.mapHeight
      const size = Math.random() * 10 + 5
      this.ctx.fillRect(x, y, size, size)
    }
  }

  /**
   * 绘制路径
   */
  drawPath() {
    // 绘制路径背景
    this.ctx.strokeStyle = '#8B4513'
    this.ctx.lineWidth = 40
    this.ctx.lineCap = 'round'
    this.ctx.lineJoin = 'round'

    this.ctx.beginPath()
    this.ctx.moveTo(this.path[0].x, this.path[0].y)
    for (let i = 1; i < this.path.length; i++) {
      this.ctx.lineTo(this.path[i].x, this.path[i].y)
    }
    this.ctx.stroke()

    // 绘制路径边框
    this.ctx.strokeStyle = '#654321'
    this.ctx.lineWidth = 44
    this.ctx.beginPath()
    this.ctx.moveTo(this.path[0].x, this.path[0].y)
    for (let i = 1; i < this.path.length; i++) {
      this.ctx.lineTo(this.path[i].x, this.path[i].y)
    }
    this.ctx.stroke()

    // 重新绘制路径
    this.ctx.strokeStyle = '#8B4513'
    this.ctx.lineWidth = 40
    this.ctx.beginPath()
    this.ctx.moveTo(this.path[0].x, this.path[0].y)
    for (let i = 1; i < this.path.length; i++) {
      this.ctx.lineTo(this.path[i].x, this.path[i].y)
    }
    this.ctx.stroke()
  }

  /**
   * 绘制网格
   */
  drawGrid() {
    if (!this.buildMode) return

    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)'
    this.ctx.lineWidth = 1
    this.ctx.setLineDash([2, 2])

    // 绘制垂直线
    for (let x = 0; x <= this.gridCols; x++) {
      const posX = x * this.gridSize
      this.ctx.beginPath()
      this.ctx.moveTo(posX, 0)
      this.ctx.lineTo(posX, this.mapHeight)
      this.ctx.stroke()
    }

    // 绘制水平线
    for (let y = 0; y <= this.gridRows; y++) {
      const posY = y * this.gridSize
      this.ctx.beginPath()
      this.ctx.moveTo(0, posY)
      this.ctx.lineTo(this.mapWidth, posY)
      this.ctx.stroke()
    }

    this.ctx.setLineDash([])

    // 绘制可建造区域提示
    this.drawBuildableAreas()
  }

  /**
   * 绘制可建造区域
   */
  drawBuildableAreas() {
    for (let x = 0; x < this.gridCols; x++) {
      for (let y = 0; y < this.gridRows; y++) {
        const centerX = x * this.gridSize + this.gridSize / 2
        const centerY = y * this.gridSize + this.gridSize / 2

        if (this.canBuildAt(centerX, centerY)) {
          // 可建造区域：绿色半透明
          this.ctx.fillStyle = 'rgba(0, 255, 0, 0.2)'
          this.ctx.fillRect(x * this.gridSize + 2, y * this.gridSize + 2,
                           this.gridSize - 4, this.gridSize - 4)
        } else {
          // 不可建造区域：红色半透明
          this.ctx.fillStyle = 'rgba(255, 0, 0, 0.1)'
          this.ctx.fillRect(x * this.gridSize + 2, y * this.gridSize + 2,
                           this.gridSize - 4, this.gridSize - 4)
        }
      }
    }
  }

  /**
   * 绘制建造预览
   */
  drawBuildPreview() {
    if (!this.buildMode || !this.selectedTowerType || !this.buildPreviewPos) return

    const gridPos = this.snapToGrid(this.buildPreviewPos.x, this.buildPreviewPos.y)
    const canBuild = this.canBuildAt(gridPos.x, gridPos.y)

    const colors = {
      basic: '#4169E1',
      cannon: '#FF6347',
      magic: '#9370DB'
    }

    const towerSize = this.gridSize * 0.8

    // 绘制预览塔（半透明）
    this.ctx.fillStyle = canBuild ?
      colors[this.selectedTowerType] + '80' :
      '#FF0000' + '80'  // 红色表示不能建造

    this.ctx.fillRect(gridPos.x - towerSize/2, gridPos.y - towerSize/2, towerSize, towerSize)

    // 绘制预览边框
    this.ctx.strokeStyle = canBuild ? '#FFFFFF' : '#FF0000'
    this.ctx.lineWidth = 2
    this.ctx.setLineDash([5, 5])
    this.ctx.strokeRect(gridPos.x - towerSize/2, gridPos.y - towerSize/2, towerSize, towerSize)
    this.ctx.setLineDash([])

    // 绘制射程预览
    if (canBuild) {
      const range = this.getTowerRange(this.selectedTowerType)
      this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)'
      this.ctx.lineWidth = 2
      this.ctx.setLineDash([3, 3])
      this.ctx.beginPath()
      this.ctx.arc(gridPos.x, gridPos.y, range, 0, Math.PI * 2)
      this.ctx.stroke()
      this.ctx.setLineDash([])
    }
  }

  /**
   * 绘制塔
   */
  drawTowers() {
    for (const tower of this.towers) {
      // 绘制射程（如果选中）
      if (this.selectedTower === tower) {
        // 绘制射程圈
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.6)'
        this.ctx.lineWidth = 3
        this.ctx.setLineDash([5, 5])
        this.ctx.beginPath()
        this.ctx.arc(tower.x, tower.y, tower.range, 0, Math.PI * 2)
        this.ctx.stroke()
        this.ctx.setLineDash([])

        // 绘制射程填充
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.1)'
        this.ctx.beginPath()
        this.ctx.arc(tower.x, tower.y, tower.range, 0, Math.PI * 2)
        this.ctx.fill()
      }

      // 绘制塔身（适应网格大小）
      const colors = {
        basic: '#4169E1',
        cannon: '#FF6347',
        magic: '#9370DB'
      }

      const towerSize = this.gridSize * 0.8  // 塔的大小为网格的80%
      this.ctx.fillStyle = colors[tower.type] || '#4169E1'
      this.ctx.fillRect(tower.x - towerSize/2, tower.y - towerSize/2, towerSize, towerSize)

      // 绘制塔的边框
      if (this.selectedTower === tower) {
        // 选中状态：金色边框
        this.ctx.strokeStyle = '#FFD700'
        this.ctx.lineWidth = 4
      } else {
        // 普通状态：黑色边框
        this.ctx.strokeStyle = '#000000'
        this.ctx.lineWidth = 2
      }
      this.ctx.strokeRect(tower.x - towerSize/2, tower.y - towerSize/2, towerSize, towerSize)

      // 绘制塔的类型标识
      this.ctx.fillStyle = '#FFFFFF'
      this.ctx.font = '12px Arial'
      this.ctx.textAlign = 'center'
      this.ctx.textBaseline = 'middle'
      const typeText = {
        basic: 'B',
        cannon: 'C',
        magic: 'M'
      }
      this.ctx.fillText(typeText[tower.type] || 'B', tower.x, tower.y)
    }
  }

  /**
   * 绘制敌人
   */
  drawEnemies() {
    for (const enemy of this.enemies) {
      // 绘制敌人身体
      this.ctx.fillStyle = '#FF4500'
      this.ctx.fillRect(enemy.x - 15, enemy.y - 15, 30, 30)

      // 绘制敌人边框
      this.ctx.strokeStyle = '#8B0000'
      this.ctx.lineWidth = 2
      this.ctx.strokeRect(enemy.x - 15, enemy.y - 15, 30, 30)

      // 绘制血条
      const barWidth = 30
      const barHeight = 4
      const hpPercent = enemy.hp / enemy.maxHp

      // 血条背景
      this.ctx.fillStyle = '#FF0000'
      this.ctx.fillRect(enemy.x - barWidth/2, enemy.y - 25, barWidth, barHeight)

      // 血条前景
      this.ctx.fillStyle = '#00FF00'
      this.ctx.fillRect(enemy.x - barWidth/2, enemy.y - 25, barWidth * hpPercent, barHeight)
    }
  }

  /**
   * 绘制子弹
   */
  drawBullets() {
    for (const bullet of this.bullets) {
      this.ctx.fillStyle = '#FFD700'
      this.ctx.beginPath()
      this.ctx.arc(bullet.x, bullet.y, 4, 0, Math.PI * 2)
      this.ctx.fill()

      // 绘制子弹轨迹
      this.ctx.strokeStyle = 'rgba(255, 215, 0, 0.5)'
      this.ctx.lineWidth = 2
      this.ctx.beginPath()
      this.ctx.moveTo(bullet.x, bullet.y)
      this.ctx.lineTo(bullet.x - bullet.vx * 5, bullet.y - bullet.vy * 5)
      this.ctx.stroke()
    }
  }

  /**
   * 绘制UI
   */
  drawUI() {
    // 绘制游戏信息面板
    this.drawGameHUD()

    // 绘制控制按钮
    this.drawControlButtons()

    // 绘制建造面板
    this.drawBuildPanel()

    // 绘制塔信息面板
    this.drawTowerInfo()

    // 绘制建造状态提示
    this.drawBuildStatus()


  }

  /**
   * 绘制游戏信息面板
   */
  drawGameHUD() {
    const hud = this.gameHUD

    // 更新文字内容
    hud.gold.text = `金币: ${this.gold}`
    hud.lives.text = `生命: ${this.lives}`
    hud.score.text = `分数: ${this.score}`
    hud.wave.text = `波次: ${this.currentWave}/${this.totalWaves}`

    // 绘制金币信息
    this.drawHUDItem(hud.gold, '#FFD700') // 金色

    // 绘制生命信息
    this.drawHUDItem(hud.lives, '#FF6B6B') // 红色

    // 绘制分数信息
    this.drawHUDItem(hud.score, '#4ECDC4') // 青色

    // 绘制波次信息
    this.drawHUDItem(hud.wave, '#9B59B6') // 紫色
  }

  /**
   * 绘制单个HUD项目
   */
  drawHUDItem(item, color) {
    // 背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)'
    this.ctx.fillRect(item.x, item.y, item.width, item.height)

    // 彩色边框
    this.ctx.strokeStyle = color
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(item.x, item.y, item.width, item.height)

    // 文字
    this.ctx.fillStyle = '#FFFFFF'
    this.ctx.font = '14px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText(item.text, item.x + item.width/2, item.y + item.height/2)
  }

  /**
   * 绘制控制按钮
   */
  drawControlButtons() {
    for (const buttonId in this.controlButtons) {
      const button = this.controlButtons[buttonId]
      this.drawButton(button)
    }
  }

  /**
   * 绘制建造面板
   */
  drawBuildPanel() {
    // 绘制胶囊按钮
    this.drawCapsuleButton()

    // 绘制建造按钮（只有在展开状态下才绘制）
    if (this.buildPanelExpanded) {
      for (const buttonId in this.buildButtons) {
        const button = this.buildButtons[buttonId]
        if (button.visible) {
          this.drawBuildButton(button)
        }
      }
    }
  }

  /**
   * 绘制胶囊按钮
   */
  drawCapsuleButton() {
    const button = this.capsuleButton

    // 按钮背景
    this.ctx.fillStyle = button.expanded ? 'rgba(255, 165, 0, 0.9)' : 'rgba(0, 123, 255, 0.9)'
    this.ctx.fillRect(button.x, button.y, button.width, button.height)

    // 按钮边框
    this.ctx.strokeStyle = button.expanded ? '#FF8C00' : '#007BFF'
    this.ctx.lineWidth = 3
    this.ctx.strokeRect(button.x, button.y, button.width, button.height)

    // 按钮文字
    this.ctx.fillStyle = '#FFFFFF'
    this.ctx.font = 'bold 12px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText(button.text, button.x + button.width/2, button.y + button.height/2)

    // 绘制展开/收起指示器（左右箭头）
    this.ctx.fillStyle = '#FFFFFF'
    this.ctx.font = '16px Arial'
    const indicator = button.expanded ? '◀' : '▶'
    this.ctx.fillText(indicator, button.x + button.width/2, button.y + button.height/2 + 15)
  }

  /**
   * 绘制单个建造按钮
   */
  drawBuildButton(button) {
    const canAfford = this.gold >= button.cost
    const isSelected = this.buildMode && this.selectedTowerType === button.type

    // 按钮背景
    if (isSelected) {
      // 选中状态：金色背景
      this.ctx.fillStyle = 'rgba(255, 215, 0, 0.9)'
    } else {
      this.ctx.fillStyle = canAfford ? 'rgba(0, 150, 0, 0.9)' : 'rgba(150, 0, 0, 0.9)'
    }
    this.ctx.fillRect(button.x, button.y, button.width, button.height)

    // 按钮边框
    if (isSelected) {
      // 选中状态：粗金色边框
      this.ctx.strokeStyle = '#FFD700'
      this.ctx.lineWidth = 4
    } else {
      this.ctx.strokeStyle = canAfford ? '#00FF00' : '#FF0000'
      this.ctx.lineWidth = 2
    }
    this.ctx.strokeRect(button.x, button.y, button.width, button.height)

    // 按钮文字
    this.ctx.fillStyle = isSelected ? '#000000' : '#FFFFFF'
    this.ctx.font = '10px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText(button.text, button.x + button.width/2, button.y + button.height/2 - 8)
    this.ctx.fillText(`$${button.cost}`, button.x + button.width/2, button.y + button.height/2 + 8)

    // 选中状态额外标识
    if (isSelected) {
      this.ctx.fillStyle = '#000000'
      this.ctx.font = 'bold 12px Arial'
      this.ctx.fillText('✓', button.x + button.width - 8, button.y + 8)
    }
  }



  /**
   * 绘制按钮
   */
  drawButton(button) {
    // 按钮背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)'
    this.ctx.fillRect(button.x, button.y, button.width, button.height)

    // 按钮边框
    this.ctx.strokeStyle = '#FFFFFF'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(button.x, button.y, button.width, button.height)

    // 按钮文字
    this.ctx.fillStyle = '#FFFFFF'
    this.ctx.font = '12px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText(button.text, button.x + button.width/2, button.y + button.height/2)
  }

  /**
   * 绘制塔信息面板
   */
  drawTowerInfo() {
    if (!this.selectedTower) return

    const tower = this.selectedTower
    const panelX = 10
    const panelY = this.mapHeight - 150
    const panelWidth = 200
    const panelHeight = 120

    // 面板背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)'
    this.ctx.fillRect(panelX, panelY, panelWidth, panelHeight)

    // 面板边框
    this.ctx.strokeStyle = '#FFD700'
    this.ctx.lineWidth = 3
    this.ctx.strokeRect(panelX, panelY, panelWidth, panelHeight)

    // 标题
    this.ctx.fillStyle = '#FFD700'
    this.ctx.font = 'bold 16px Arial'
    this.ctx.textAlign = 'left'
    this.ctx.textBaseline = 'top'

    const towerNames = {
      basic: '基础塔',
      cannon: '炮塔',
      magic: '魔法塔'
    }

    this.ctx.fillText(towerNames[tower.type] || '未知塔', panelX + 10, panelY + 10)

    // 属性信息
    this.ctx.fillStyle = '#FFFFFF'
    this.ctx.font = '14px Arial'

    this.ctx.fillText(`伤害: ${tower.damage}`, panelX + 10, panelY + 35)
    this.ctx.fillText(`射程: ${tower.range}`, panelX + 10, panelY + 55)
    this.ctx.fillText(`射速: ${(1000/tower.fireRate).toFixed(1)}/秒`, panelX + 10, panelY + 75)

    // 操作提示
    this.ctx.fillStyle = '#CCCCCC'
    this.ctx.font = '12px Arial'
    this.ctx.fillText('再次点击取消选择', panelX + 10, panelY + 95)
  }

  /**
   * 绘制建造状态提示
   */
  drawBuildStatus() {
    if (!this.buildMode || !this.selectedTowerType) return

    const centerX = this.mapWidth / 2
    const tipY = 80

    // 提示背景
    this.ctx.fillStyle = 'rgba(255, 215, 0, 0.9)'
    this.ctx.fillRect(centerX - 100, tipY - 15, 200, 30)

    // 提示边框
    this.ctx.strokeStyle = '#FFD700'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(centerX - 100, tipY - 15, 200, 30)

    // 提示文字
    this.ctx.fillStyle = '#000000'
    this.ctx.font = 'bold 14px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'

    const towerNames = {
      basic: '基础塔',
      cannon: '炮塔',
      magic: '魔法塔'
    }

    this.ctx.fillText(`点击空地建造${towerNames[this.selectedTowerType]} | 再次点击按钮取消`, centerX, tipY)
  }

  /**
   * 绘制游戏状态
   */
  drawGameStatus() {
    const centerX = this.mapWidth / 2
    const centerY = this.mapHeight / 2

    if (this.gameState === 'victory') {
      // 胜利提示
      this.ctx.fillStyle = 'rgba(0, 255, 0, 0.8)'
      this.ctx.fillRect(centerX - 150, centerY - 50, 300, 100)

      this.ctx.strokeStyle = '#00FF00'
      this.ctx.lineWidth = 3
      this.ctx.strokeRect(centerX - 150, centerY - 50, 300, 100)

      this.ctx.fillStyle = '#FFFFFF'
      this.ctx.font = 'bold 24px Arial'
      this.ctx.textAlign = 'center'
      this.ctx.textBaseline = 'middle'
      this.ctx.fillText('胜利！', centerX, centerY - 10)
      this.ctx.font = '16px Arial'
      this.ctx.fillText('点击返回按钮退出', centerX, centerY + 20)

    } else if (this.gameState === 'defeat') {
      // 失败提示
      this.ctx.fillStyle = 'rgba(255, 0, 0, 0.8)'
      this.ctx.fillRect(centerX - 150, centerY - 50, 300, 100)

      this.ctx.strokeStyle = '#FF0000'
      this.ctx.lineWidth = 3
      this.ctx.strokeRect(centerX - 150, centerY - 50, 300, 100)

      this.ctx.fillStyle = '#FFFFFF'
      this.ctx.font = 'bold 24px Arial'
      this.ctx.textAlign = 'center'
      this.ctx.textBaseline = 'middle'
      this.ctx.fillText('失败！', centerX, centerY - 10)
      this.ctx.font = '16px Arial'
      this.ctx.fillText('点击返回按钮退出', centerX, centerY + 20)

    } else if (this.enemiesSpawned >= this.maxEnemiesPerWave && this.enemies.length === 0 && this.currentWave < this.totalWaves) {
      // 波次间隔提示
      this.ctx.fillStyle = 'rgba(0, 0, 255, 0.7)'
      this.ctx.fillRect(centerX - 100, centerY - 30, 200, 60)

      this.ctx.strokeStyle = '#0080FF'
      this.ctx.lineWidth = 2
      this.ctx.strokeRect(centerX - 100, centerY - 30, 200, 60)

      this.ctx.fillStyle = '#FFFFFF'
      this.ctx.font = 'bold 18px Arial'
      this.ctx.textAlign = 'center'
      this.ctx.textBaseline = 'middle'
      this.ctx.fillText(`第${this.currentWave}波即将开始`, centerX, centerY)
    }
  }
  
  /**
   * 更新游戏逻辑
   */
  update(deltaTime) {
    if (this.isPaused) return

    // 更新动画时间
    this.animationTime += deltaTime * this.gameSpeed

    // 生成敌人
    this.spawnEnemies(deltaTime)

    // 更新敌人
    this.updateEnemies(deltaTime)

    // 更新塔攻击
    this.updateTowers(deltaTime)

    // 更新子弹
    this.updateBullets(deltaTime)

    // 检查游戏状态
    this.checkGameState()
  }

  /**
   * 生成敌人
   */
  spawnEnemies(deltaTime) {
    if (this.gameState !== 'playing') return
    if (this.enemiesSpawned >= this.maxEnemiesPerWave) return

    const now = Date.now()
    if (now - this.lastEnemySpawnTime > this.enemySpawnInterval) {
      this.spawnEnemy()
      this.lastEnemySpawnTime = now
      this.enemiesSpawned++

      console.log(`生成敌人 ${this.enemiesSpawned}/${this.maxEnemiesPerWave}`)
    }
  }

  /**
   * 生成单个敌人
   */
  spawnEnemy() {
    // 根据波次增加敌人强度
    const waveMultiplier = 1 + (this.currentWave - 1) * 0.3

    const enemy = {
      id: Date.now(),
      x: this.path[0].x,
      y: this.path[0].y,
      hp: Math.floor(50 * waveMultiplier),
      maxHp: Math.floor(50 * waveMultiplier),
      speed: 1 + (this.currentWave - 1) * 0.1,
      pathIndex: 0,
      pathProgress: 0,
      reward: Math.floor(10 * waveMultiplier)
    }

    this.enemies.push(enemy)
  }

  /**
   * 更新敌人
   */
  updateEnemies(deltaTime) {
    for (let i = this.enemies.length - 1; i >= 0; i--) {
      const enemy = this.enemies[i]

      // 移动敌人
      this.moveEnemyAlongPath(enemy, deltaTime)

      // 检查是否到达终点
      if (enemy.pathIndex >= this.path.length - 1) {
        this.enemies.splice(i, 1)
        this.lives--
        console.log(`敌人到达终点，剩余生命: ${this.lives}`)
      }

      // 检查是否死亡
      if (enemy.hp <= 0) {
        this.enemies.splice(i, 1)
        this.gold += enemy.reward
        this.score += enemy.reward
        console.log(`击杀敌人，获得${enemy.reward}金币`)
      }
    }
  }

  /**
   * 移动敌人沿路径
   */
  moveEnemyAlongPath(enemy, deltaTime) {
    if (enemy.pathIndex >= this.path.length - 1) return

    const currentPoint = this.path[enemy.pathIndex]
    const nextPoint = this.path[enemy.pathIndex + 1]

    const dx = nextPoint.x - currentPoint.x
    const dy = nextPoint.y - currentPoint.y
    const distance = Math.sqrt(dx * dx + dy * dy)

    if (distance === 0) {
      enemy.pathIndex++
      return
    }

    const moveDistance = enemy.speed * deltaTime * this.gameSpeed / 16.67 // 假设60fps
    enemy.pathProgress += moveDistance / distance

    if (enemy.pathProgress >= 1) {
      enemy.pathProgress = 0
      enemy.pathIndex++
    }

    enemy.x = currentPoint.x + dx * enemy.pathProgress
    enemy.y = currentPoint.y + dy * enemy.pathProgress
  }

  /**
   * 更新塔
   */
  updateTowers(deltaTime) {
    const now = Date.now()

    for (const tower of this.towers) {
      if (now - tower.lastFireTime < tower.fireRate) continue

      // 寻找目标
      const target = this.findNearestEnemy(tower.x, tower.y, tower.range)
      if (target) {
        this.fireBullet(tower, target)
        tower.lastFireTime = now
      }
    }
  }

  /**
   * 寻找最近的敌人
   */
  findNearestEnemy(x, y, range) {
    let nearest = null
    let minDistance = range

    for (const enemy of this.enemies) {
      const distance = Math.sqrt((enemy.x - x) * (enemy.x - x) + (enemy.y - y) * (enemy.y - y))
      if (distance < minDistance) {
        minDistance = distance
        nearest = enemy
      }
    }

    return nearest
  }

  /**
   * 发射子弹
   */
  fireBullet(tower, target) {
    const dx = target.x - tower.x
    const dy = target.y - tower.y
    const distance = Math.sqrt(dx * dx + dy * dy)

    const bullet = {
      id: Date.now(),
      x: tower.x,
      y: tower.y,
      vx: (dx / distance) * 5, // 子弹速度
      vy: (dy / distance) * 5,
      damage: tower.damage,
      target: target
    }

    this.bullets.push(bullet)
  }

  /**
   * 更新子弹
   */
  updateBullets(deltaTime) {
    for (let i = this.bullets.length - 1; i >= 0; i--) {
      const bullet = this.bullets[i]

      // 移动子弹
      bullet.x += bullet.vx * deltaTime * this.gameSpeed / 16.67
      bullet.y += bullet.vy * deltaTime * this.gameSpeed / 16.67

      // 检查是否击中目标
      if (bullet.target && this.enemies.includes(bullet.target)) {
        const distance = Math.sqrt(
          (bullet.x - bullet.target.x) * (bullet.x - bullet.target.x) +
          (bullet.y - bullet.target.y) * (bullet.y - bullet.target.y)
        )

        if (distance < 20) {
          // 击中目标
          bullet.target.hp -= bullet.damage
          this.bullets.splice(i, 1)
          continue
        }
      }

      // 检查是否超出屏幕
      if (bullet.x < 0 || bullet.x > this.mapWidth ||
          bullet.y < 0 || bullet.y > this.mapHeight) {
        this.bullets.splice(i, 1)
      }
    }
  }

  /**
   * 检查游戏状态
   */
  checkGameState() {
    if (this.lives <= 0) {
      this.gameState = 'defeat'
      console.log('游戏失败')
      return
    }

    // 检查波次是否完成
    if (this.enemiesSpawned >= this.maxEnemiesPerWave && this.enemies.length === 0) {
      this.completeWave()
    }
  }

  /**
   * 完成当前波次
   */
  completeWave() {
    this.currentWave++
    this.enemiesSpawned = 0

    if (this.currentWave > this.totalWaves) {
      // 游戏胜利
      this.gameState = 'victory'
      console.log('游戏胜利！')
    } else {
      // 开始下一波
      console.log(`第${this.currentWave}波开始！`)
      // 给玩家一些奖励金币
      this.gold += 50
      // 稍微延迟开始下一波
      setTimeout(() => {
        if (this.gameState === 'playing') {
          this.lastEnemySpawnTime = Date.now()
        }
      }, 3000)
    }
  }

  /**
   * 设置场景切换回调
   */
  setSceneChangeCallback(callback) {
    this.sceneChangeCallback = callback
  }

  /**
   * 销毁场景
   */
  destroy() {
    if (this.touchStartHandler) {
      wx.offTouchStart(this.touchStartHandler)
    }
    if (this.touchEndHandler) {
      wx.offTouchEnd(this.touchEndHandler)
    }
    if (this.touchMoveHandler) {
      wx.offTouchMove(this.touchMoveHandler)
    }

    // 停止背景音乐
    AudioManager.stopMusic()
  }
}

module.exports = BattleScene
