const ScreenAdapter = require('../utils/ScreenAdapter.js')
const BackButton = require('../components/BackButton.js')
const AudioManager = require('../utils/AudioManager.js')
const DataManager = require('../utils/DataManager.js')

/**
 * 关卡战斗场景
 */
class BattleScene {
  constructor(canvas, ctx, levelId = 1) {
    this.canvas = canvas
    this.ctx = ctx
    this.adapter = new ScreenAdapter()
    
    // 创建返回按钮
    this.backButtonComponent = new BackButton(require('../utils/ImageManager.js'))
    
    // 关卡信息
    this.levelId = levelId
    this.levelName = `关卡 ${levelId}`
    
    // 游戏状态
    this.gameState = 'playing' // preparing, playing, paused, victory, defeat
    this.isPaused = false
    this.gameSpeed = 1 // 1x, 2x
    
    // 游戏数据
    this.gold = 200
    this.lives = 20
    this.score = 0
    this.currentWave = 0
    this.totalWaves = 5
    
    // 地图配置（3:2比例，比屏幕大）
    const screenWidth = this.adapter.getWindowWidth()
    const screenHeight = this.adapter.getWindowHeight()

    // 计算合适的地图尺寸（比屏幕大1.5倍，保持3:2比例）
    const mapScale = 1.5
    this.mapWidth = Math.max(screenWidth * mapScale, screenHeight * mapScale * 1.5)
    this.mapHeight = this.mapWidth / 1.5  // 3:2比例

    // 网格系统（适应更大地图）
    this.gridSize = 50  // 适中的网格大小，适合大地图
    this.gridCols = Math.floor(this.mapWidth / this.gridSize)
    this.gridRows = Math.floor(this.mapHeight / this.gridSize)
    this.showGrid = false  // 是否显示网格

    // 禁区设置（外围2格禁区）
    this.borderZone = 2  // 上下左右各2格禁区
    this.playableGridCols = this.gridCols - this.borderZone * 2  // 可用列数
    this.playableGridRows = this.gridRows - this.borderZone * 2  // 可用行数

    console.log(`地图尺寸: ${this.mapWidth}x${this.mapHeight}`)
    console.log(`总网格数量: ${this.gridCols}列 x ${this.gridRows}行 = ${this.gridCols * this.gridRows}个网格`)
    console.log(`可用网格数量: ${this.playableGridCols}列 x ${this.playableGridRows}行 = ${this.playableGridCols * this.playableGridRows}个网格`)
    console.log(`外围禁区: 上下左右各${this.borderZone}格`)
    console.log(`网格大小: ${this.gridSize}x${this.gridSize}像素`)
    
    // 路径配置（简单的L型路径）
    this.path = this.createPath()
    
    // 游戏实体
    this.towers = []
    this.enemies = []
    this.bullets = []
    this.effects = []
    
    // UI配置
    this.setupUI()
    
    // 波次管理
    this.waveStartTime = 0
    this.enemySpawnQueue = []
    this.lastEnemySpawnTime = 0
    this.enemySpawnInterval = 2000 // 2秒生成一个敌人
    this.enemiesSpawned = 0
    this.maxEnemiesPerWave = 10
    
    // 建造模式（新的交互方式）
    this.buildMode = false
    this.selectedTowerType = null  // 初始时不选择任何塔类型
    this.buildPreviewPos = null

    // 塔选择菜单
    this.towerSelectMode = false   // 是否显示塔选择菜单
    this.towerSelectPos = null     // 塔选择菜单的位置

    // 地图视图控制
    this.camera = {
      x: 0,           // 摄像机X偏移
      y: 0,           // 摄像机Y偏移
      scale: 0.8,     // 初始缩放（稍微缩小以显示更多区域）
      minScale: 0.3,  // 最小缩放（可以看到整个大地图）
      maxScale: 2.0,  // 最大缩放
      isDragging: false,
      lastTouchX: 0,
      lastTouchY: 0,
      dragStartX: 0,
      dragStartY: 0
    }

    // 初始化摄像机位置（居中显示）
    this.initializeCamera()

    // 选中的塔
    this.selectedTower = null
    
    // 动画和效果
    this.animationTime = 0
    this.particles = []
    
    // 绑定事件
    this.bindEvents()
    
    // 播放背景音乐
    this.playBackgroundMusic()

    // 开始游戏
    this.startGame()

    console.log(`战斗场景初始化完成 - 关卡${levelId}`)
  }
  
  /**
   * 创建路径（敌人移动路径）
   */
  createPath() {
    // 返回路径网格的中心点，敌人沿着这些点移动
    return this.getPathCenterPoints()
  }
  
  /**
   * 设置UI
   */
  setupUI() {
    const windowWidth = this.adapter.getWindowWidth()
    const windowHeight = this.adapter.getWindowHeight()
    
    // 游戏信息面板（顶部居中一行显示）
    const panelWidth = 100
    const panelHeight = 30
    const spacing = 5
    const totalWidth = panelWidth * 4 + spacing * 3 // 4个面板 + 3个间距
    const startX = (windowWidth - totalWidth) / 2

    this.gameHUD = {
      gold: {
        x: startX,
        y: 10,
        width: panelWidth,
        height: panelHeight,
        text: `金币: ${this.gold}`
      },
      lives: {
        x: startX + panelWidth + spacing,
        y: 10,
        width: panelWidth,
        height: panelHeight,
        text: `生命: ${this.lives}`
      },
      score: {
        x: startX + (panelWidth + spacing) * 2,
        y: 10,
        width: panelWidth,
        height: panelHeight,
        text: `分数: ${this.score}`
      },
      wave: {
        x: startX + (panelWidth + spacing) * 3,
        y: 10,
        width: panelWidth,
        height: panelHeight,
        text: `波次: ${this.currentWave}/${this.totalWaves}`
      }
    }
    
    // 控制按钮（右侧中间）
    this.controlButtons = {
      pause: {
        x: windowWidth - 60,
        y: windowHeight / 2 - 40,
        width: 50,
        height: 35,
        text: '暂停',
        action: 'pause'
      },
      speed: {
        x: windowWidth - 60,
        y: windowHeight / 2 + 5,
        width: 50,
        height: 35,
        text: '1x',
        action: 'speed'
      }
    }
    
    // 塔类型配置（用于塔选择菜单）
    this.towerTypes = {
      basic: {
        name: '基础塔',
        cost: 50,
        color: '#4169E1',
        damage: 20,
        range: 80,
        fireRate: 1000
      },
      cannon: {
        name: '炮塔',
        cost: 100,
        color: '#FF6347',
        damage: 50,
        range: 70,
        fireRate: 1500
      },
      magic: {
        name: '魔法塔',
        cost: 150,
        color: '#9370DB',
        damage: 30,
        range: 100,
        fireRate: 800
      }
    }

    // 保留buildButtons用于兼容性（某些方法可能还在使用）
    this.buildButtons = {
      basic: { cost: 50, text: '基础塔' },
      cannon: { cost: 100, text: '炮塔' },
      magic: { cost: 150, text: '魔法塔' }
    }
    

  }
  
  /**
   * 播放背景音乐
   */
  playBackgroundMusic() {
    const settings = DataManager.getSettings()
    if (settings.musicEnabled) {
      AudioManager.playMusic('battle_theme', {
        volume: settings.musicVolume,
        loop: true
      })
    }
  }

  /**
   * 开始游戏
   */
  startGame() {
    this.gameState = 'playing'
    this.currentWave = 1
    this.lastEnemySpawnTime = Date.now()

    // 立即生成第一个敌人
    this.spawnEnemy()

    console.log('游戏开始！')
  }
  
  /**
   * 绑定事件
   */
  bindEvents() {
    this.touchStartHandler = this.onTouchStart.bind(this)
    this.touchEndHandler = this.onTouchEnd.bind(this)
    this.touchMoveHandler = this.onTouchMove.bind(this)

    wx.onTouchStart(this.touchStartHandler)
    wx.onTouchEnd(this.touchEndHandler)
    wx.onTouchMove(this.touchMoveHandler)
  }
  
  /**
   * 触摸开始事件
   */
  onTouchStart(e) {
    const touch = e.touches[0]
    const pos = this.adapter.convertTouchToCanvas(touch)
    
    // 检查返回按钮
    if (this.backButtonComponent.isPointInButton(pos)) {
      this.backButtonComponent.setPressed(true)
      return
    }

    // 检查缩放控制按钮
    if (this.handleZoomControls(pos)) {
      return
    }
    
    // 检查控制按钮
    for (let buttonId in this.controlButtons) {
      const button = this.controlButtons[buttonId]
      if (this.isPointInRect(pos, button)) {
        this.handleControlButton(buttonId)
        return
      }
    }
    
    // 旧的建造面板已移除

    // 检查塔选择菜单
    if (this.towerSelectMode) {
      const towerButtons = this.getTowerSelectButtons()
      for (const [towerType, button] of Object.entries(towerButtons)) {
        if (this.isPointInRect(pos, button)) {
          this.selectTowerType(towerType)
          return
        }
      }
    }

    // 旧的建造按钮系统已移除，现在使用点击空地选择塔的方式
    
    // 开始拖拽
    this.camera.isDragging = true
    this.camera.dragStartX = pos.x
    this.camera.dragStartY = pos.y
    this.camera.lastTouchX = pos.x
    this.camera.lastTouchY = pos.y

    // 检查地图点击（转换为世界坐标）
    const worldPos = this.screenToWorld(pos)
    this.handleMapClick(worldPos)
  }
  
  /**
   * 触摸结束事件
   */
  onTouchEnd(e) {
    const touch = e.changedTouches[0]
    const pos = this.adapter.convertTouchToCanvas(touch)

    // 检查返回按钮
    if (this.backButtonComponent.isPointInButton(pos)) {
      this.backButtonComponent.setPressed(false)
      // 返回冒险地图
      if (this.sceneChangeCallback) {
        this.sceneChangeCallback('adventure-map')
      }
      return
    }

    this.backButtonComponent.setPressed(false)

    // 停止拖拽
    this.camera.isDragging = false
  }

  /**
   * 触摸移动事件
   */
  onTouchMove(e) {
    const touch = e.touches[0]
    const pos = this.adapter.convertTouchToCanvas(touch)

    // 处理拖拽
    if (this.camera.isDragging) {
      const deltaX = pos.x - this.camera.lastTouchX
      const deltaY = pos.y - this.camera.lastTouchY

      // 更新摄像机位置
      this.camera.x -= deltaX
      this.camera.y -= deltaY

      // 限制摄像机边界
      this.constrainCamera()

      this.camera.lastTouchX = pos.x
      this.camera.lastTouchY = pos.y
    }

    // 新的建造系统不需要预览，直接点击空地选择塔
  }
  
  /**
   * 检查点是否在矩形内
   */
  isPointInRect(point, rect) {
    return point.x >= rect.x && point.x <= rect.x + rect.width &&
           point.y >= rect.y && point.y <= rect.y + rect.height
  }
  
  /**
   * 处理控制按钮
   */
  handleControlButton(buttonId) {
    switch (buttonId) {
      case 'pause':
        this.togglePause()
        break
      case 'speed':
        this.toggleSpeed()
        break
    }
  }
  
  // 旧的建造面板方法已移除，现在使用点击空地选择塔的方式
  
  /**
   * 处理地图点击
   */
  handleMapClick(pos) {
    if (this.towerSelectMode) {
      // 如果在塔选择模式下，点击其他地方取消选择
      this.cancelTowerSelect()
    } else {
      // 检查点击的位置
      const gridPos = this.snapToGrid(pos.x, pos.y)

      // 检查是否点击了已有的塔
      const clickedTower = this.getTowerAt(gridPos.x, gridPos.y)
      if (clickedTower) {
        this.selectTower(gridPos.x, gridPos.y)
      } else if (this.canBuildAt(gridPos.x, gridPos.y)) {
        // 点击空地，显示塔选择菜单
        this.showTowerSelectMenu(gridPos.x, gridPos.y)
      }
    }
  }
  
  /**
   * 切换暂停状态
   */
  togglePause() {
    this.isPaused = !this.isPaused
    this.controlButtons.pause.text = this.isPaused ? '继续' : '暂停'
    console.log(this.isPaused ? '游戏暂停' : '游戏继续')
  }
  
  /**
   * 切换游戏速度
   */
  toggleSpeed() {
    this.gameSpeed = this.gameSpeed === 1 ? 2 : 1
    this.controlButtons.speed.text = `${this.gameSpeed}x`
    console.log(`游戏速度: ${this.gameSpeed}x`)
  }
  
  /**
   * 尝试建造塔
   */
  tryBuildTower(x, y) {
    // 将点击位置对齐到网格
    const gridPos = this.snapToGrid(x, y)

    // 检查是否可以在此位置建造
    if (!this.canBuildAt(gridPos.x, gridPos.y)) {
      console.log('此位置无法建造')
      return false
    }

    // 获取建造费用
    const button = this.buildButtons[this.selectedTowerType]
    if (this.gold < button.cost) {
      console.log('金币不足')
      return false
    }

    // 显示建造确认界面
    this.showBuildConfirm(gridPos.x, gridPos.y)
    return true
  }

  /**
   * 显示建造确认
   */
  showBuildConfirm(x, y) {
    this.buildConfirmMode = true
    this.buildConfirmPos = { x: x, y: y }
    console.log(`显示建造确认: ${this.selectedTowerType}塔`)
  }

  /**
   * 取消建造确认
   */
  cancelBuildConfirm() {
    this.buildConfirmMode = false
    this.buildConfirmPos = null
    console.log('取消建造确认')
  }

  /**
   * 显示塔选择菜单
   */
  showTowerSelectMenu(x, y) {
    this.towerSelectMode = true
    this.towerSelectPos = { x: x, y: y }
    console.log(`显示塔选择菜单在位置: (${x}, ${y})`)
  }

  /**
   * 取消塔选择菜单
   */
  cancelTowerSelect() {
    this.towerSelectMode = false
    this.towerSelectPos = null
    console.log('取消塔选择菜单')
  }

  /**
   * 选择塔类型并建造
   */
  selectTowerType(towerType) {
    if (this.towerSelectPos) {
      const towerConfig = this.towerTypes[towerType]
      if (this.gold >= towerConfig.cost) {
        this.buildTower(this.towerSelectPos.x, this.towerSelectPos.y, towerType, towerConfig.cost)
        this.cancelTowerSelect()
      } else {
        console.log(`金币不足，无法建造${towerConfig.name}`)
      }
    }
  }

  /**
   * 获取塔选择按钮
   */
  getTowerSelectButtons() {
    if (!this.towerSelectPos) return {}

    // 将世界坐标转换为屏幕坐标
    const screenPos = this.worldToScreen(this.towerSelectPos)
    const centerX = screenPos.x
    const centerY = screenPos.y

    const buttonSize = 60
    const spacing = 10

    return {
      basic: {
        x: centerX - buttonSize - spacing,
        y: centerY - buttonSize/2,
        width: buttonSize,
        height: buttonSize
      },
      cannon: {
        x: centerX - buttonSize/2,
        y: centerY - buttonSize - spacing,
        width: buttonSize,
        height: buttonSize
      },
      magic: {
        x: centerX + spacing,
        y: centerY - buttonSize/2,
        width: buttonSize,
        height: buttonSize
      }
    }
  }

  /**
   * 将坐标对齐到网格中心
   */
  snapToGrid(x, y) {
    const gridX = Math.floor(x / this.gridSize)
    const gridY = Math.floor(y / this.gridSize)

    return {
      x: gridX * this.gridSize + this.gridSize / 2,
      y: gridY * this.gridSize + this.gridSize / 2
    }
  }

  /**
   * 检查是否可以在指定位置建造
   */
  canBuildAt(x, y) {
    // 获取网格坐标
    const gridX = Math.floor(x / this.gridSize)
    const gridY = Math.floor(y / this.gridSize)

    // 检查是否在外围禁区
    if (this.isInBorderZone(gridX, gridY)) {
      return false
    }

    // 检查是否在路径上
    if (this.isOnPath(x, y)) {
      return false
    }

    // 检查是否与其他塔重叠
    if (this.isPositionOccupied(x, y)) {
      return false
    }

    // 检查是否在地图边界内
    if (x < this.gridSize/2 || x > this.mapWidth - this.gridSize/2 ||
        y < this.gridSize/2 || y > this.mapHeight - this.gridSize/2) {
      return false
    }

    return true
  }

  /**
   * 检查网格是否在外围禁区
   */
  isInBorderZone(gridX, gridY) {
    return gridX < this.borderZone ||
           gridX >= this.gridCols - this.borderZone ||
           gridY < this.borderZone ||
           gridY >= this.gridRows - this.borderZone
  }
  
  /**
   * 检查位置是否在路径上（基于网格）
   */
  isOnPath(x, y) {
    // 获取点击位置所在的网格
    const gridX = Math.floor(x / this.gridSize)
    const gridY = Math.floor(y / this.gridSize)

    // 检查这个网格是否被路径占用
    return this.isGridOnPath(gridX, gridY)
  }

  /**
   * 检查网格是否在路径上
   */
  isGridOnPath(gridX, gridY) {
    // 基于可用区域计算路径（完全在可用区域内）
    const startCol = this.borderZone
    const endCol = this.gridCols - this.borderZone - 1
    const startRow = this.borderZone
    const endRow = this.gridRows - this.borderZone - 1

    // 复杂S型路径的各个段
    const pathRow1 = startRow + Math.floor(this.playableGridRows * 0.2)  // 第一段
    const pathRow2 = startRow + Math.floor(this.playableGridRows * 0.5)  // 第二段
    const pathRow3 = startRow + Math.floor(this.playableGridRows * 0.8)  // 第三段
    const pathCol1 = startCol + Math.floor(this.playableGridCols * 0.3)  // 第一个转弯
    const pathCol2 = startCol + Math.floor(this.playableGridCols * 0.7)  // 第二个转弯

    // 检查是否在路径上（S型路径）
    // 第一段：水平线（从可用区域左边缘到第一个转弯点）
    if (gridY === pathRow1 && gridX >= startCol && gridX <= pathCol1) {
      return true
    }

    // 第二段：垂直线（从第一段到第二段）
    if (gridX === pathCol1 && gridY >= pathRow1 && gridY <= pathRow2) {
      return true
    }

    // 第三段：水平线（从第一个转弯点到第二个转弯点）
    if (gridY === pathRow2 && gridX >= pathCol1 && gridX <= pathCol2) {
      return true
    }

    // 第四段：垂直线（从第二段到第三段）
    if (gridX === pathCol2 && gridY >= pathRow2 && gridY <= pathRow3) {
      return true
    }

    // 第五段：水平线（从第二个转弯点到可用区域右边缘）
    if (gridY === pathRow3 && gridX >= pathCol2 && gridX <= endCol) {
      return true
    }

    return false
  }

  /**
   * 获取路径网格的中心点（用于敌人移动）
   */
  getPathCenterPoints() {
    const g = this.gridSize

    // 基于可用区域计算路径（完全在可用区域内）
    const startCol = this.borderZone
    const endCol = this.gridCols - this.borderZone - 1
    const startRow = this.borderZone
    const endRow = this.gridRows - this.borderZone - 1

    // 设计更复杂的路径（适合大地图）
    const pathRow1 = startRow + Math.floor(this.playableGridRows * 0.2)  // 第一段
    const pathRow2 = startRow + Math.floor(this.playableGridRows * 0.5)  // 第二段
    const pathRow3 = startRow + Math.floor(this.playableGridRows * 0.8)  // 第三段
    const pathCol1 = startCol + Math.floor(this.playableGridCols * 0.3)  // 第一个转弯
    const pathCol2 = startCol + Math.floor(this.playableGridCols * 0.7)  // 第二个转弯

    // 返回更复杂的S型路径（包含入口和出口）
    return [
      { x: 0, y: pathRow1 * g + g/2 },                    // 入口：从左侧禁区进入
      { x: startCol * g + g/2, y: pathRow1 * g + g/2 },   // 进入可用区域
      { x: pathCol1 * g + g/2, y: pathRow1 * g + g/2 },   // 第一段水平
      { x: pathCol1 * g + g/2, y: pathRow2 * g + g/2 },   // 第一个转弯（向下）
      { x: pathCol2 * g + g/2, y: pathRow2 * g + g/2 },   // 第二段水平
      { x: pathCol2 * g + g/2, y: pathRow3 * g + g/2 },   // 第二个转弯（向下）
      { x: endCol * g + g/2, y: pathRow3 * g + g/2 },     // 第三段水平
      { x: this.mapWidth, y: pathRow3 * g + g/2 }         // 出口：到右侧禁区
    ]
  }


  
  /**
   * 计算点到线段的距离
   */
  distanceToLineSegment(px, py, x1, y1, x2, y2) {
    const dx = x2 - x1
    const dy = y2 - y1
    const length = Math.sqrt(dx * dx + dy * dy)
    
    if (length === 0) {
      return Math.sqrt((px - x1) * (px - x1) + (py - y1) * (py - y1))
    }
    
    const t = Math.max(0, Math.min(1, ((px - x1) * dx + (py - y1) * dy) / (length * length)))
    const projX = x1 + t * dx
    const projY = y1 + t * dy
    
    return Math.sqrt((px - projX) * (px - projX) + (py - projY) * (py - projY))
  }
  
  /**
   * 检查位置是否被占用
   */
  isPositionOccupied(x, y) {
    // 使用网格系统，检查是否有塔在同一网格位置
    for (const tower of this.towers) {
      const distance = Math.sqrt((tower.x - x) * (tower.x - x) + (tower.y - y) * (tower.y - y))
      if (distance < this.gridSize / 2) {
        return true
      }
    }

    return false
  }

  /**
   * 获取指定位置的塔
   */
  getTowerAt(x, y) {
    for (const tower of this.towers) {
      const distance = Math.sqrt((tower.x - x) ** 2 + (tower.y - y) ** 2)
      if (distance < this.gridSize / 2) {
        return tower
      }
    }
    return null
  }
  
  /**
   * 建造塔
   */
  buildTower(x, y, type, cost) {
    const tower = {
      id: Date.now(),
      x: x,
      y: y,
      type: type,
      level: 1,
      damage: this.getTowerDamage(type),
      range: this.getTowerRange(type),
      fireRate: this.getTowerFireRate(type),
      lastFireTime: 0,
      target: null
    }
    
    this.towers.push(tower)
    this.gold -= cost
    // 保持建造模式和选中状态，方便连续建造同类型塔
    // this.buildMode = false
    // this.selectedTowerType = null

    console.log(`建造了${type}塔，剩余金币: ${this.gold}，可继续建造`)
  }
  
  /**
   * 获取塔的伤害
   */
  getTowerDamage(type) {
    const damages = {
      basic: 20,
      cannon: 50,
      magic: 30
    }
    return damages[type] || 20
  }
  
  /**
   * 获取塔的射程
   */
  getTowerRange(type) {
    const ranges = {
      basic: 80,
      cannon: 100,
      magic: 120
    }
    return ranges[type] || 80
  }
  
  /**
   * 获取塔的射速
   */
  getTowerFireRate(type) {
    const fireRates = {
      basic: 1000, // 1秒
      cannon: 2000, // 2秒
      magic: 1500  // 1.5秒
    }
    return fireRates[type] || 1000
  }
  


  /**
   * 选择塔
   */
  selectTower(x, y) {
    let clickedTower = null

    // 查找点击的塔（使用网格范围）
    for (const tower of this.towers) {
      const distance = Math.sqrt((tower.x - x) * (tower.x - x) + (tower.y - y) * (tower.y - y))
      if (distance < this.gridSize / 2) {
        clickedTower = tower
        break
      }
    }

    if (clickedTower) {
      if (this.selectedTower === clickedTower) {
        // 如果点击的是已选中的塔，取消选择
        this.selectedTower = null
        console.log('取消选择塔')
      } else {
        // 选择新的塔
        this.selectedTower = clickedTower
        console.log(`选中了${clickedTower.type}塔 - 伤害:${clickedTower.damage} 射程:${clickedTower.range}`)
      }
    } else {
      // 点击空地，取消选择
      this.selectedTower = null
    }
  }
  
  /**
   * 渲染场景
   */
  render() {
    // 应用像素比缩放（与其他场景保持一致）
    this.ctx.save()
    this.ctx.scale(this.adapter.getPixelRatio(), this.adapter.getPixelRatio())

    // 应用摄像机变换（只对游戏世界，不包括UI）
    this.ctx.save()
    this.ctx.translate(-this.camera.x, -this.camera.y)
    this.ctx.scale(this.camera.scale, this.camera.scale)

    // 绘制游戏世界
    this.drawBackground()
    this.drawPath()
    this.drawBorderZone()
    this.drawGrid()
    this.drawTowers()
    this.drawEnemies()
    this.drawBullets()

    this.ctx.restore()

    // 绘制UI（不受摄像机影响）
    this.drawUI()
    this.drawTowerSelectMenu()  // 塔选择菜单在UI层绘制
    this.backButtonComponent.render(this.ctx)
    this.drawGameStatus()

    // 绘制缩放控制按钮
    this.drawZoomControls()

    // 绘制小地图
    this.drawMiniMap()

    this.ctx.restore()
  }

  /**
   * 绘制背景
   */
  drawBackground() {
    // 绘制草地背景
    this.ctx.fillStyle = '#90EE90'
    this.ctx.fillRect(0, 0, this.mapWidth, this.mapHeight)

    // 添加一些纹理效果
    this.ctx.fillStyle = 'rgba(34, 139, 34, 0.1)'
    for (let i = 0; i < 50; i++) {
      const x = Math.random() * this.mapWidth
      const y = Math.random() * this.mapHeight
      const size = Math.random() * 10 + 5
      this.ctx.fillRect(x, y, size, size)
    }
  }

  /**
   * 绘制路径（基于网格，只在可用区域内）
   */
  drawPath() {
    // 绘制路径网格（只在可用区域内）
    for (let x = 0; x < this.gridCols; x++) {
      for (let y = 0; y < this.gridRows; y++) {
        if (this.isGridOnPath(x, y)) {
          const gridX = x * this.gridSize
          const gridY = y * this.gridSize

          // 绘制路径背景
          this.ctx.fillStyle = '#654321'
          this.ctx.fillRect(gridX, gridY, this.gridSize, this.gridSize)

          // 绘制路径主体
          this.ctx.fillStyle = '#8B4513'
          this.ctx.fillRect(gridX + 2, gridY + 2, this.gridSize - 4, this.gridSize - 4)

          // 绘制路径中心线（敌人移动轨迹）
          this.ctx.fillStyle = '#D2691E'
          const centerX = gridX + this.gridSize / 2
          const centerY = gridY + this.gridSize / 2
          this.ctx.beginPath()
          this.ctx.arc(centerX, centerY, 2, 0, Math.PI * 2)
          this.ctx.fill()
        }
      }
    }

    // 绘制入口和出口连接线（在禁区内）
    this.drawPathConnections()
  }

  /**
   * 绘制路径连接线（入口和出口）
   */
  drawPathConnections() {
    const g = this.gridSize
    const startCol = this.borderZone
    const endCol = this.gridCols - this.borderZone - 1
    const pathRow1 = this.borderZone + Math.floor(this.playableGridRows * 0.2)  // 入口行
    const pathRow3 = this.borderZone + Math.floor(this.playableGridRows * 0.8)  // 出口行

    this.ctx.strokeStyle = '#8B4513'
    this.ctx.lineWidth = this.gridSize * 0.6
    this.ctx.lineCap = 'round'

    // 入口连接线（从左边缘到可用区域）
    this.ctx.beginPath()
    this.ctx.moveTo(0, pathRow1 * g + g/2)
    this.ctx.lineTo(startCol * g + g/2, pathRow1 * g + g/2)
    this.ctx.stroke()

    // 出口连接线（从可用区域到右边缘）
    this.ctx.beginPath()
    this.ctx.moveTo(endCol * g + g/2, pathRow3 * g + g/2)
    this.ctx.lineTo(this.mapWidth, pathRow3 * g + g/2)
    this.ctx.stroke()
  }

  /**
   * 绘制网格
   */
  drawGrid() {
    if (!this.buildMode) return

    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)'
    this.ctx.lineWidth = 1
    this.ctx.setLineDash([2, 2])

    // 绘制垂直线
    for (let x = 0; x <= this.gridCols; x++) {
      const posX = x * this.gridSize
      this.ctx.beginPath()
      this.ctx.moveTo(posX, 0)
      this.ctx.lineTo(posX, this.mapHeight)
      this.ctx.stroke()
    }

    // 绘制水平线
    for (let y = 0; y <= this.gridRows; y++) {
      const posY = y * this.gridSize
      this.ctx.beginPath()
      this.ctx.moveTo(0, posY)
      this.ctx.lineTo(this.mapWidth, posY)
      this.ctx.stroke()
    }

    this.ctx.setLineDash([])

    // 绘制可建造区域提示
    this.drawBuildableAreas()
  }

  /**
   * 绘制可建造区域
   */
  drawBuildableAreas() {
    for (let x = 0; x < this.gridCols; x++) {
      for (let y = 0; y < this.gridRows; y++) {
        const centerX = x * this.gridSize + this.gridSize / 2
        const centerY = y * this.gridSize + this.gridSize / 2

        if (this.isInBorderZone(x, y)) {
          // 外围禁区：不绘制任何覆盖，让禁区背景显示
          continue
        } else if (this.isGridOnPath(x, y)) {
          // 路径网格：不绘制覆盖，让路径本身显示
          continue
        } else if (this.canBuildAt(centerX, centerY)) {
          // 可建造区域：绿色半透明
          this.ctx.fillStyle = 'rgba(0, 255, 0, 0.2)'
          this.ctx.fillRect(x * this.gridSize + 2, y * this.gridSize + 2,
                           this.gridSize - 4, this.gridSize - 4)
        } else {
          // 不可建造区域（已占用）：红色半透明
          this.ctx.fillStyle = 'rgba(255, 0, 0, 0.1)'
          this.ctx.fillRect(x * this.gridSize + 2, y * this.gridSize + 2,
                           this.gridSize - 4, this.gridSize - 4)
        }
      }
    }
  }

  /**
   * 绘制建造预览
   */
  drawBuildPreview() {
    if (!this.buildMode || !this.selectedTowerType || !this.buildPreviewPos || this.buildConfirmMode) return

    const gridPos = this.snapToGrid(this.buildPreviewPos.x, this.buildPreviewPos.y)
    const canBuild = this.canBuildAt(gridPos.x, gridPos.y)

    const colors = {
      basic: '#4169E1',
      cannon: '#FF6347',
      magic: '#9370DB'
    }

    const towerSize = this.gridSize * 0.75

    // 检查是否有足够金币
    const button = this.buildButtons[this.selectedTowerType]
    const hasEnoughGold = button && this.gold >= button.cost

    // 绘制预览塔（半透明）
    if (canBuild && hasEnoughGold) {
      this.ctx.fillStyle = colors[this.selectedTowerType] + '80'
    } else {
      this.ctx.fillStyle = '#FF0000' + '80'  // 红色表示不能建造或金币不足
    }

    this.ctx.fillRect(gridPos.x - towerSize/2, gridPos.y - towerSize/2, towerSize, towerSize)

    // 绘制预览边框
    this.ctx.strokeStyle = (canBuild && hasEnoughGold) ? '#FFFFFF' : '#FF0000'
    this.ctx.lineWidth = 2
    this.ctx.setLineDash([5, 5])
    this.ctx.strokeRect(gridPos.x - towerSize/2, gridPos.y - towerSize/2, towerSize, towerSize)
    this.ctx.setLineDash([])

    // 绘制射程预览（只有在可建造且金币足够时才显示）
    if (canBuild && hasEnoughGold) {
      const range = this.getTowerRange(this.selectedTowerType)
      this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)'
      this.ctx.lineWidth = 2
      this.ctx.setLineDash([3, 3])
      this.ctx.beginPath()
      this.ctx.arc(gridPos.x, gridPos.y, range, 0, Math.PI * 2)
      this.ctx.stroke()
      this.ctx.setLineDash([])
    }
  }

  /**
   * 绘制塔
   */
  drawTowers() {
    for (const tower of this.towers) {
      // 绘制射程（如果选中）
      if (this.selectedTower === tower) {
        // 绘制射程圈
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.6)'
        this.ctx.lineWidth = 3
        this.ctx.setLineDash([5, 5])
        this.ctx.beginPath()
        this.ctx.arc(tower.x, tower.y, tower.range, 0, Math.PI * 2)
        this.ctx.stroke()
        this.ctx.setLineDash([])

        // 绘制射程填充
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.1)'
        this.ctx.beginPath()
        this.ctx.arc(tower.x, tower.y, tower.range, 0, Math.PI * 2)
        this.ctx.fill()
      }

      // 绘制塔身（适应网格大小）
      const colors = {
        basic: '#4169E1',
        cannon: '#FF6347',
        magic: '#9370DB'
      }

      const towerSize = this.gridSize * 0.75  // 塔的大小为网格的75%
      this.ctx.fillStyle = colors[tower.type] || '#4169E1'
      this.ctx.fillRect(tower.x - towerSize/2, tower.y - towerSize/2, towerSize, towerSize)

      // 绘制塔的边框
      if (this.selectedTower === tower) {
        // 选中状态：金色边框
        this.ctx.strokeStyle = '#FFD700'
        this.ctx.lineWidth = 4
      } else {
        // 普通状态：黑色边框
        this.ctx.strokeStyle = '#000000'
        this.ctx.lineWidth = 2
      }
      this.ctx.strokeRect(tower.x - towerSize/2, tower.y - towerSize/2, towerSize, towerSize)

      // 绘制塔的类型标识
      this.ctx.fillStyle = '#FFFFFF'
      this.ctx.font = '12px Arial'
      this.ctx.textAlign = 'center'
      this.ctx.textBaseline = 'middle'
      const typeText = {
        basic: 'B',
        cannon: 'C',
        magic: 'M'
      }
      this.ctx.fillText(typeText[tower.type] || 'B', tower.x, tower.y)
    }
  }

  /**
   * 绘制敌人
   */
  drawEnemies() {
    for (const enemy of this.enemies) {
      // 绘制敌人身体（圆形，适应更小的网格）
      const enemySize = this.gridSize * 0.35  // 敌人大小为网格的35%
      this.ctx.fillStyle = '#FF4500'
      this.ctx.beginPath()
      this.ctx.arc(enemy.x, enemy.y, enemySize, 0, Math.PI * 2)
      this.ctx.fill()

      // 绘制敌人边框
      this.ctx.strokeStyle = '#8B0000'
      this.ctx.lineWidth = 2
      this.ctx.beginPath()
      this.ctx.arc(enemy.x, enemy.y, enemySize, 0, Math.PI * 2)
      this.ctx.stroke()

      // 绘制敌人眼睛（显示移动方向）
      const eyeSize = enemySize * 0.2
      const eyeOffset = enemySize * 0.4
      this.ctx.fillStyle = '#FFFFFF'
      this.ctx.beginPath()
      this.ctx.arc(enemy.x - eyeOffset, enemy.y - eyeOffset, eyeSize, 0, Math.PI * 2)
      this.ctx.fill()
      this.ctx.beginPath()
      this.ctx.arc(enemy.x + eyeOffset, enemy.y - eyeOffset, eyeSize, 0, Math.PI * 2)
      this.ctx.fill()

      // 绘制血条
      const barWidth = enemySize * 2.5
      const barHeight = 3
      const hpPercent = enemy.hp / enemy.maxHp

      // 血条背景
      this.ctx.fillStyle = '#FF0000'
      this.ctx.fillRect(enemy.x - barWidth/2, enemy.y - enemySize - 8, barWidth, barHeight)

      // 血条前景
      this.ctx.fillStyle = '#00FF00'
      this.ctx.fillRect(enemy.x - barWidth/2, enemy.y - enemySize - 8, barWidth * hpPercent, barHeight)
    }
  }

  /**
   * 绘制子弹
   */
  drawBullets() {
    for (const bullet of this.bullets) {
      this.ctx.fillStyle = '#FFD700'
      this.ctx.beginPath()
      this.ctx.arc(bullet.x, bullet.y, 4, 0, Math.PI * 2)
      this.ctx.fill()

      // 绘制子弹轨迹
      this.ctx.strokeStyle = 'rgba(255, 215, 0, 0.5)'
      this.ctx.lineWidth = 2
      this.ctx.beginPath()
      this.ctx.moveTo(bullet.x, bullet.y)
      this.ctx.lineTo(bullet.x - bullet.vx * 5, bullet.y - bullet.vy * 5)
      this.ctx.stroke()
    }
  }

  /**
   * 绘制UI
   */
  drawUI() {
    // 绘制游戏信息面板
    this.drawGameHUD()

    // 绘制控制按钮
    this.drawControlButtons()

    // 绘制塔信息面板
    this.drawTowerInfo()
  }

  /**
   * 绘制游戏信息面板
   */
  drawGameHUD() {
    const hud = this.gameHUD

    // 更新文字内容
    hud.gold.text = `金币: ${this.gold}`
    hud.lives.text = `生命: ${this.lives}`
    hud.score.text = `分数: ${this.score}`
    hud.wave.text = `波次: ${this.currentWave}/${this.totalWaves}`

    // 绘制金币信息
    this.drawHUDItem(hud.gold, '#FFD700') // 金色

    // 绘制生命信息
    this.drawHUDItem(hud.lives, '#FF6B6B') // 红色

    // 绘制分数信息
    this.drawHUDItem(hud.score, '#4ECDC4') // 青色

    // 绘制波次信息
    this.drawHUDItem(hud.wave, '#9B59B6') // 紫色
  }

  /**
   * 绘制单个HUD项目
   */
  drawHUDItem(item, color) {
    // 背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)'
    this.ctx.fillRect(item.x, item.y, item.width, item.height)

    // 彩色边框
    this.ctx.strokeStyle = color
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(item.x, item.y, item.width, item.height)

    // 文字
    this.ctx.fillStyle = '#FFFFFF'
    this.ctx.font = '14px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText(item.text, item.x + item.width/2, item.y + item.height/2)
  }

  /**
   * 绘制控制按钮
   */
  drawControlButtons() {
    for (const buttonId in this.controlButtons) {
      const button = this.controlButtons[buttonId]
      this.drawButton(button)
    }
  }

  /**
   * 绘制建造面板
   */
  drawBuildPanel() {
    // 绘制胶囊按钮
    this.drawCapsuleButton()

    // 绘制建造按钮（只有在展开状态下才绘制）
    if (this.buildPanelExpanded) {
      for (const buttonId in this.buildButtons) {
        const button = this.buildButtons[buttonId]
        if (button.visible) {
          this.drawBuildButton(button)
        }
      }
    }
  }

  /**
   * 绘制胶囊按钮
   */
  drawCapsuleButton() {
    const button = this.capsuleButton

    // 按钮背景
    this.ctx.fillStyle = button.expanded ? 'rgba(255, 165, 0, 0.9)' : 'rgba(0, 123, 255, 0.9)'
    this.ctx.fillRect(button.x, button.y, button.width, button.height)

    // 按钮边框
    this.ctx.strokeStyle = button.expanded ? '#FF8C00' : '#007BFF'
    this.ctx.lineWidth = 3
    this.ctx.strokeRect(button.x, button.y, button.width, button.height)

    // 按钮文字
    this.ctx.fillStyle = '#FFFFFF'
    this.ctx.font = 'bold 12px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText(button.text, button.x + button.width/2, button.y + button.height/2)

    // 绘制展开/收起指示器（左右箭头）
    this.ctx.fillStyle = '#FFFFFF'
    this.ctx.font = '16px Arial'
    const indicator = button.expanded ? '◀' : '▶'
    this.ctx.fillText(indicator, button.x + button.width/2, button.y + button.height/2 + 15)
  }

  /**
   * 绘制单个建造按钮
   */
  drawBuildButton(button) {
    const canAfford = this.gold >= button.cost
    const isSelected = this.buildMode && this.selectedTowerType === button.type

    // 按钮背景
    if (isSelected) {
      // 选中状态：金色背景
      this.ctx.fillStyle = 'rgba(255, 215, 0, 0.9)'
    } else {
      this.ctx.fillStyle = canAfford ? 'rgba(0, 150, 0, 0.9)' : 'rgba(150, 0, 0, 0.9)'
    }
    this.ctx.fillRect(button.x, button.y, button.width, button.height)

    // 按钮边框
    if (isSelected) {
      // 选中状态：粗金色边框
      this.ctx.strokeStyle = '#FFD700'
      this.ctx.lineWidth = 4
    } else {
      this.ctx.strokeStyle = canAfford ? '#00FF00' : '#FF0000'
      this.ctx.lineWidth = 2
    }
    this.ctx.strokeRect(button.x, button.y, button.width, button.height)

    // 按钮文字
    this.ctx.fillStyle = isSelected ? '#000000' : '#FFFFFF'
    this.ctx.font = '10px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText(button.text, button.x + button.width/2, button.y + button.height/2 - 8)
    this.ctx.fillText(`$${button.cost}`, button.x + button.width/2, button.y + button.height/2 + 8)

    // 选中状态额外标识
    if (isSelected) {
      this.ctx.fillStyle = '#000000'
      this.ctx.font = 'bold 12px Arial'
      this.ctx.fillText('✓', button.x + button.width - 8, button.y + 8)
    }
  }



  /**
   * 绘制按钮
   */
  drawButton(button) {
    // 按钮背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)'
    this.ctx.fillRect(button.x, button.y, button.width, button.height)

    // 按钮边框
    this.ctx.strokeStyle = '#FFFFFF'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(button.x, button.y, button.width, button.height)

    // 按钮文字
    this.ctx.fillStyle = '#FFFFFF'
    this.ctx.font = '12px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText(button.text, button.x + button.width/2, button.y + button.height/2)
  }

  /**
   * 绘制塔信息面板
   */
  drawTowerInfo() {
    if (!this.selectedTower) return

    const tower = this.selectedTower
    const panelX = 10
    const panelY = this.mapHeight - 150
    const panelWidth = 200
    const panelHeight = 120

    // 面板背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)'
    this.ctx.fillRect(panelX, panelY, panelWidth, panelHeight)

    // 面板边框
    this.ctx.strokeStyle = '#FFD700'
    this.ctx.lineWidth = 3
    this.ctx.strokeRect(panelX, panelY, panelWidth, panelHeight)

    // 标题
    this.ctx.fillStyle = '#FFD700'
    this.ctx.font = 'bold 16px Arial'
    this.ctx.textAlign = 'left'
    this.ctx.textBaseline = 'top'

    const towerNames = {
      basic: '基础塔',
      cannon: '炮塔',
      magic: '魔法塔'
    }

    this.ctx.fillText(towerNames[tower.type] || '未知塔', panelX + 10, panelY + 10)

    // 属性信息
    this.ctx.fillStyle = '#FFFFFF'
    this.ctx.font = '14px Arial'

    this.ctx.fillText(`伤害: ${tower.damage}`, panelX + 10, panelY + 35)
    this.ctx.fillText(`射程: ${tower.range}`, panelX + 10, panelY + 55)
    this.ctx.fillText(`射速: ${(1000/tower.fireRate).toFixed(1)}/秒`, panelX + 10, panelY + 75)

    // 操作提示
    this.ctx.fillStyle = '#CCCCCC'
    this.ctx.font = '12px Arial'
    this.ctx.fillText('再次点击取消选择', panelX + 10, panelY + 95)
  }

  /**
   * 绘制建造状态提示
   */
  drawBuildStatus() {
    if (!this.buildMode || !this.selectedTowerType || this.buildConfirmMode) return

    const centerX = this.mapWidth / 2
    const tipY = 80

    // 提示背景
    this.ctx.fillStyle = 'rgba(255, 215, 0, 0.9)'
    this.ctx.fillRect(centerX - 100, tipY - 15, 200, 30)

    // 提示边框
    this.ctx.strokeStyle = '#FFD700'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(centerX - 100, tipY - 15, 200, 30)

    // 提示文字
    this.ctx.fillStyle = '#000000'
    this.ctx.font = 'bold 14px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'

    const towerNames = {
      basic: '基础塔',
      cannon: '炮塔',
      magic: '魔法塔'
    }

    // 检查是否有足够金币
    const button = this.buildButtons[this.selectedTowerType]
    if (button && this.gold >= button.cost) {
      this.ctx.fillText(`点击空地建造${towerNames[this.selectedTowerType]} | 再次点击按钮取消`, centerX, tipY)
    } else {
      // 金币不足的提示
      this.ctx.fillStyle = '#FF0000'
      this.ctx.fillText(`金币不足，无法建造${towerNames[this.selectedTowerType]}`, centerX, tipY)
    }
  }

  /**
   * 绘制建造确认界面
   */
  drawBuildConfirm() {
    if (!this.buildConfirmMode || !this.buildConfirmPos) return

    // 将世界坐标转换为屏幕坐标
    const screenPos = this.worldToScreen(this.buildConfirmPos)
    const centerX = screenPos.x
    const centerY = screenPos.y
    const button = this.buildButtons[this.selectedTowerType]

    // 绘制确认塔的预览（高亮显示）
    const colors = {
      basic: '#4169E1',
      cannon: '#FF6347',
      magic: '#9370DB'
    }

    const towerSize = this.gridSize * 0.75

    // 绘制塔预览
    this.ctx.fillStyle = colors[this.selectedTowerType]
    this.ctx.fillRect(centerX - towerSize/2, centerY - towerSize/2, towerSize, towerSize)

    // 绘制高亮边框
    this.ctx.strokeStyle = '#FFD700'
    this.ctx.lineWidth = 4
    this.ctx.strokeRect(centerX - towerSize/2, centerY - towerSize/2, towerSize, towerSize)

    // 绘制射程
    const range = this.getTowerRange(this.selectedTowerType)
    this.ctx.strokeStyle = 'rgba(255, 215, 0, 0.5)'
    this.ctx.lineWidth = 3
    this.ctx.setLineDash([5, 5])
    this.ctx.beginPath()
    this.ctx.arc(centerX, centerY, range, 0, Math.PI * 2)
    this.ctx.stroke()
    this.ctx.setLineDash([])

    // 绘制确认面板背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)'
    this.ctx.fillRect(centerX - 80, centerY + 20, 160, 80)

    // 绘制确认面板边框
    this.ctx.strokeStyle = '#FFD700'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(centerX - 80, centerY + 20, 160, 80)

    // 绘制塔信息
    const towerNames = {
      basic: '基础塔',
      cannon: '炮塔',
      magic: '魔法塔'
    }

    this.ctx.fillStyle = '#FFFFFF'
    this.ctx.font = 'bold 14px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'top'
    this.ctx.fillText(`建造${towerNames[this.selectedTowerType]}`, centerX, centerY + 25)
    this.ctx.font = '12px Arial'
    this.ctx.fillText(`费用: ${button.cost}金币`, centerX, centerY + 45)

    // 绘制确认按钮
    const buttons = this.getBuildConfirmButtons()

    // 确认按钮
    this.ctx.fillStyle = 'rgba(0, 150, 0, 0.9)'
    this.ctx.fillRect(buttons.confirm.x, buttons.confirm.y, buttons.confirm.width, buttons.confirm.height)
    this.ctx.strokeStyle = '#00FF00'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(buttons.confirm.x, buttons.confirm.y, buttons.confirm.width, buttons.confirm.height)
    this.ctx.fillStyle = '#FFFFFF'
    this.ctx.font = 'bold 12px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText('确定', buttons.confirm.x + buttons.confirm.width/2, buttons.confirm.y + buttons.confirm.height/2)

    // 取消按钮
    this.ctx.fillStyle = 'rgba(150, 0, 0, 0.9)'
    this.ctx.fillRect(buttons.cancel.x, buttons.cancel.y, buttons.cancel.width, buttons.cancel.height)
    this.ctx.strokeStyle = '#FF0000'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(buttons.cancel.x, buttons.cancel.y, buttons.cancel.width, buttons.cancel.height)
    this.ctx.fillStyle = '#FFFFFF'
    this.ctx.font = 'bold 12px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText('取消', buttons.cancel.x + buttons.cancel.width/2, buttons.cancel.y + buttons.cancel.height/2)
  }

  /**
   * 绘制缩放控制按钮
   */
  drawZoomControls() {
    const windowWidth = this.adapter.getWindowWidth()
    const windowHeight = this.adapter.getWindowHeight()

    // 缩放按钮位置（左侧中间）
    const zoomInBtn = {
      x: 10,
      y: windowHeight / 2 - 60,
      width: 40,
      height: 40
    }

    const zoomOutBtn = {
      x: 10,
      y: windowHeight / 2 - 10,
      width: 40,
      height: 40
    }

    const resetBtn = {
      x: 10,
      y: windowHeight / 2 + 40,
      width: 40,
      height: 40
    }

    // 绘制放大按钮
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)'
    this.ctx.fillRect(zoomInBtn.x, zoomInBtn.y, zoomInBtn.width, zoomInBtn.height)
    this.ctx.strokeStyle = '#FFFFFF'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(zoomInBtn.x, zoomInBtn.y, zoomInBtn.width, zoomInBtn.height)
    this.ctx.fillStyle = '#FFFFFF'
    this.ctx.font = 'bold 20px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText('+', zoomInBtn.x + zoomInBtn.width/2, zoomInBtn.y + zoomInBtn.height/2)

    // 绘制缩小按钮
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)'
    this.ctx.fillRect(zoomOutBtn.x, zoomOutBtn.y, zoomOutBtn.width, zoomOutBtn.height)
    this.ctx.strokeStyle = '#FFFFFF'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(zoomOutBtn.x, zoomOutBtn.y, zoomOutBtn.width, zoomOutBtn.height)
    this.ctx.fillStyle = '#FFFFFF'
    this.ctx.font = 'bold 20px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText('-', zoomOutBtn.x + zoomOutBtn.width/2, zoomOutBtn.y + zoomOutBtn.height/2)

    // 绘制重置按钮
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)'
    this.ctx.fillRect(resetBtn.x, resetBtn.y, resetBtn.width, resetBtn.height)
    this.ctx.strokeStyle = '#FFFFFF'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(resetBtn.x, resetBtn.y, resetBtn.width, resetBtn.height)
    this.ctx.fillStyle = '#FFFFFF'
    this.ctx.font = 'bold 12px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText('1:1', resetBtn.x + resetBtn.width/2, resetBtn.y + resetBtn.height/2)

    // 显示当前缩放比例
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)'
    this.ctx.fillRect(10, windowHeight / 2 + 90, 60, 25)
    this.ctx.strokeStyle = '#FFFFFF'
    this.ctx.lineWidth = 1
    this.ctx.strokeRect(10, windowHeight / 2 + 90, 60, 25)
    this.ctx.fillStyle = '#FFFFFF'
    this.ctx.font = '12px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText(`${(this.camera.scale * 100).toFixed(0)}%`, 40, windowHeight / 2 + 102)
  }

  /**
   * 处理缩放控制按钮
   */
  handleZoomControls(pos) {
    const windowHeight = this.adapter.getWindowHeight()

    const zoomInBtn = { x: 10, y: windowHeight / 2 - 60, width: 40, height: 40 }
    const zoomOutBtn = { x: 10, y: windowHeight / 2 - 10, width: 40, height: 40 }
    const resetBtn = { x: 10, y: windowHeight / 2 + 40, width: 40, height: 40 }

    if (this.isPointInRect(pos, zoomInBtn)) {
      this.zoomIn()
      return true
    }

    if (this.isPointInRect(pos, zoomOutBtn)) {
      this.zoomOut()
      return true
    }

    if (this.isPointInRect(pos, resetBtn)) {
      this.resetZoom()
      return true
    }

    return false
  }

  /**
   * 放大
   */
  zoomIn() {
    this.camera.scale = Math.min(this.camera.scale * 1.2, this.camera.maxScale)
    this.constrainCamera()
  }

  /**
   * 缩小
   */
  zoomOut() {
    this.camera.scale = Math.max(this.camera.scale / 1.2, this.camera.minScale)
    this.constrainCamera()
  }

  /**
   * 重置缩放
   */
  resetZoom() {
    this.camera.scale = 1
    this.camera.x = 0
    this.camera.y = 0
  }

  /**
   * 屏幕坐标转世界坐标
   */
  screenToWorld(screenPos) {
    return {
      x: (screenPos.x + this.camera.x) / this.camera.scale,
      y: (screenPos.y + this.camera.y) / this.camera.scale
    }
  }

  /**
   * 世界坐标转屏幕坐标
   */
  worldToScreen(worldPos) {
    return {
      x: worldPos.x * this.camera.scale - this.camera.x,
      y: worldPos.y * this.camera.scale - this.camera.y
    }
  }

  /**
   * 限制摄像机边界
   */
  constrainCamera() {
    const windowWidth = this.adapter.getWindowWidth()
    const windowHeight = this.adapter.getWindowHeight()

    // 计算缩放后的地图尺寸
    const scaledMapWidth = this.mapWidth * this.camera.scale
    const scaledMapHeight = this.mapHeight * this.camera.scale

    // 限制X轴
    if (scaledMapWidth <= windowWidth) {
      // 地图比屏幕小，居中显示
      this.camera.x = (scaledMapWidth - windowWidth) / 2
    } else {
      // 地图比屏幕大，限制边界
      this.camera.x = Math.max(0, Math.min(this.camera.x, scaledMapWidth - windowWidth))
    }

    // 限制Y轴
    if (scaledMapHeight <= windowHeight) {
      // 地图比屏幕小，居中显示
      this.camera.y = (scaledMapHeight - windowHeight) / 2
    } else {
      // 地图比屏幕大，限制边界
      this.camera.y = Math.max(0, Math.min(this.camera.y, scaledMapHeight - windowHeight))
    }
  }

  /**
   * 初始化摄像机位置
   */
  initializeCamera() {
    const screenWidth = this.adapter.getWindowWidth()
    const screenHeight = this.adapter.getWindowHeight()

    // 计算居中位置
    const scaledMapWidth = this.mapWidth * this.camera.scale
    const scaledMapHeight = this.mapHeight * this.camera.scale

    // 居中显示地图
    this.camera.x = Math.max(0, (scaledMapWidth - screenWidth) / 2)
    this.camera.y = Math.max(0, (scaledMapHeight - screenHeight) / 2)

    console.log(`地图尺寸: ${this.mapWidth}x${this.mapHeight} (3:2比例)`)
    console.log(`屏幕尺寸: ${screenWidth}x${screenHeight}`)
    console.log(`初始缩放: ${this.camera.scale}`)
    console.log(`摄像机位置: (${this.camera.x}, ${this.camera.y})`)
  }

  /**
   * 绘制小地图
   */
  drawMiniMap() {
    const windowWidth = this.adapter.getWindowWidth()
    const windowHeight = this.adapter.getWindowHeight()

    // 小地图尺寸和位置（右上角）
    const miniMapWidth = 120
    const miniMapHeight = 80  // 保持3:2比例
    const miniMapX = windowWidth - miniMapWidth - 10
    const miniMapY = 10

    // 绘制小地图背景
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)'
    this.ctx.fillRect(miniMapX, miniMapY, miniMapWidth, miniMapHeight)
    this.ctx.strokeStyle = '#FFFFFF'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(miniMapX, miniMapY, miniMapWidth, miniMapHeight)

    // 计算缩放比例
    const scaleX = miniMapWidth / this.mapWidth
    const scaleY = miniMapHeight / this.mapHeight

    // 绘制禁区
    this.ctx.fillStyle = 'rgba(255, 0, 0, 0.3)'
    const borderSize = this.borderZone * this.gridSize
    this.ctx.fillRect(miniMapX, miniMapY, miniMapWidth, borderSize * scaleY)  // 上
    this.ctx.fillRect(miniMapX, miniMapY + miniMapHeight - borderSize * scaleY, miniMapWidth, borderSize * scaleY)  // 下
    this.ctx.fillRect(miniMapX, miniMapY, borderSize * scaleX, miniMapHeight)  // 左
    this.ctx.fillRect(miniMapX + miniMapWidth - borderSize * scaleX, miniMapY, borderSize * scaleX, miniMapHeight)  // 右

    // 绘制路径（简化）
    this.ctx.strokeStyle = '#8B4513'
    this.ctx.lineWidth = 2
    const path = this.getPathCenterPoints()
    this.ctx.beginPath()
    for (let i = 0; i < path.length; i++) {
      const x = miniMapX + path[i].x * scaleX
      const y = miniMapY + path[i].y * scaleY
      if (i === 0) {
        this.ctx.moveTo(x, y)
      } else {
        this.ctx.lineTo(x, y)
      }
    }
    this.ctx.stroke()

    // 绘制当前视野范围
    const screenWidth = this.adapter.getWindowWidth()
    const screenHeight = this.adapter.getWindowHeight()
    const viewX = miniMapX + (this.camera.x / this.camera.scale) * scaleX
    const viewY = miniMapY + (this.camera.y / this.camera.scale) * scaleY
    const viewWidth = (screenWidth / this.camera.scale) * scaleX
    const viewHeight = (screenHeight / this.camera.scale) * scaleY

    this.ctx.strokeStyle = '#FFD700'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(viewX, viewY, viewWidth, viewHeight)

    // 绘制塔位置（小点）
    this.ctx.fillStyle = '#4169E1'
    for (const tower of this.towers) {
      const x = miniMapX + tower.x * scaleX
      const y = miniMapY + tower.y * scaleY
      this.ctx.beginPath()
      this.ctx.arc(x, y, 2, 0, Math.PI * 2)
      this.ctx.fill()
    }

    // 绘制敌人位置（小点）
    this.ctx.fillStyle = '#FF4500'
    for (const enemy of this.enemies) {
      const x = miniMapX + enemy.x * scaleX
      const y = miniMapY + enemy.y * scaleY
      this.ctx.beginPath()
      this.ctx.arc(x, y, 1, 0, Math.PI * 2)
      this.ctx.fill()
    }
  }

  /**
   * 绘制塔选择菜单
   */
  drawTowerSelectMenu() {
    if (!this.towerSelectMode || !this.towerSelectPos) return

    const buttons = this.getTowerSelectButtons()
    const towerTypeKeys = ['basic', 'cannon', 'magic']

    // 绘制每个塔选择按钮
    for (const towerType of towerTypeKeys) {
      const button = buttons[towerType]
      if (!button) continue

      const towerConfig = this.towerTypes[towerType]
      const canAfford = this.gold >= towerConfig.cost

      // 绘制按钮背景
      this.ctx.fillStyle = canAfford ?
        'rgba(0, 0, 0, 0.8)' :
        'rgba(100, 0, 0, 0.8)'
      this.ctx.fillRect(button.x, button.y, button.width, button.height)

      // 绘制按钮边框
      this.ctx.strokeStyle = canAfford ? '#FFD700' : '#FF0000'
      this.ctx.lineWidth = 3
      this.ctx.strokeRect(button.x, button.y, button.width, button.height)

      // 绘制塔图标
      const iconSize = button.width * 0.6
      const iconX = button.x + (button.width - iconSize) / 2
      const iconY = button.y + (button.height - iconSize) / 2

      this.ctx.fillStyle = canAfford ? towerConfig.color : '#666666'
      this.ctx.fillRect(iconX, iconY, iconSize, iconSize)

      // 绘制塔名称
      this.ctx.fillStyle = canAfford ? '#FFFFFF' : '#999999'
      this.ctx.font = 'bold 10px Arial'
      this.ctx.textAlign = 'center'
      this.ctx.textBaseline = 'top'
      this.ctx.fillText(towerConfig.name,
        button.x + button.width/2,
        button.y + button.height + 2)

      // 绘制费用
      this.ctx.font = '9px Arial'
      this.ctx.fillText(`${towerConfig.cost}金币`,
        button.x + button.width/2,
        button.y + button.height + 15)
    }

    // 绘制中心指示点
    const screenPos = this.worldToScreen(this.towerSelectPos)
    this.ctx.fillStyle = '#FFD700'
    this.ctx.beginPath()
    this.ctx.arc(screenPos.x, screenPos.y, 5, 0, Math.PI * 2)
    this.ctx.fill()

    // 绘制提示文字
    this.ctx.fillStyle = '#FFFFFF'
    this.ctx.font = 'bold 14px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'bottom'
    this.ctx.fillText('选择要建造的塔', screenPos.x, screenPos.y - 80)
  }

  /**
   * 绘制外围禁区边界
   */
  drawBorderZone() {
    const borderSize = this.borderZone * this.gridSize

    // 绘制禁区边界线
    this.ctx.strokeStyle = 'rgba(255, 0, 0, 0.5)'
    this.ctx.lineWidth = 2
    this.ctx.setLineDash([10, 5])

    // 上边界
    this.ctx.beginPath()
    this.ctx.moveTo(0, borderSize)
    this.ctx.lineTo(this.mapWidth, borderSize)
    this.ctx.stroke()

    // 下边界
    this.ctx.beginPath()
    this.ctx.moveTo(0, this.mapHeight - borderSize)
    this.ctx.lineTo(this.mapWidth, this.mapHeight - borderSize)
    this.ctx.stroke()

    // 左边界
    this.ctx.beginPath()
    this.ctx.moveTo(borderSize, 0)
    this.ctx.lineTo(borderSize, this.mapHeight)
    this.ctx.stroke()

    // 右边界
    this.ctx.beginPath()
    this.ctx.moveTo(this.mapWidth - borderSize, 0)
    this.ctx.lineTo(this.mapWidth - borderSize, this.mapHeight)
    this.ctx.stroke()

    this.ctx.setLineDash([])

    // 绘制禁区标识
    this.ctx.fillStyle = 'rgba(255, 0, 0, 0.1)'

    // 上禁区
    this.ctx.fillRect(0, 0, this.mapWidth, borderSize)
    // 下禁区
    this.ctx.fillRect(0, this.mapHeight - borderSize, this.mapWidth, borderSize)
    // 左禁区
    this.ctx.fillRect(0, borderSize, borderSize, this.mapHeight - borderSize * 2)
    // 右禁区
    this.ctx.fillRect(this.mapWidth - borderSize, borderSize, borderSize, this.mapHeight - borderSize * 2)
  }

  /**
   * 绘制游戏状态
   */
  drawGameStatus() {
    const centerX = this.mapWidth / 2
    const centerY = this.mapHeight / 2

    if (this.gameState === 'victory') {
      // 胜利提示
      this.ctx.fillStyle = 'rgba(0, 255, 0, 0.8)'
      this.ctx.fillRect(centerX - 150, centerY - 50, 300, 100)

      this.ctx.strokeStyle = '#00FF00'
      this.ctx.lineWidth = 3
      this.ctx.strokeRect(centerX - 150, centerY - 50, 300, 100)

      this.ctx.fillStyle = '#FFFFFF'
      this.ctx.font = 'bold 24px Arial'
      this.ctx.textAlign = 'center'
      this.ctx.textBaseline = 'middle'
      this.ctx.fillText('胜利！', centerX, centerY - 10)
      this.ctx.font = '16px Arial'
      this.ctx.fillText('点击返回按钮退出', centerX, centerY + 20)

    } else if (this.gameState === 'defeat') {
      // 失败提示
      this.ctx.fillStyle = 'rgba(255, 0, 0, 0.8)'
      this.ctx.fillRect(centerX - 150, centerY - 50, 300, 100)

      this.ctx.strokeStyle = '#FF0000'
      this.ctx.lineWidth = 3
      this.ctx.strokeRect(centerX - 150, centerY - 50, 300, 100)

      this.ctx.fillStyle = '#FFFFFF'
      this.ctx.font = 'bold 24px Arial'
      this.ctx.textAlign = 'center'
      this.ctx.textBaseline = 'middle'
      this.ctx.fillText('失败！', centerX, centerY - 10)
      this.ctx.font = '16px Arial'
      this.ctx.fillText('点击返回按钮退出', centerX, centerY + 20)

    } else if (this.enemiesSpawned >= this.maxEnemiesPerWave && this.enemies.length === 0 && this.currentWave < this.totalWaves) {
      // 波次间隔提示
      this.ctx.fillStyle = 'rgba(0, 0, 255, 0.7)'
      this.ctx.fillRect(centerX - 100, centerY - 30, 200, 60)

      this.ctx.strokeStyle = '#0080FF'
      this.ctx.lineWidth = 2
      this.ctx.strokeRect(centerX - 100, centerY - 30, 200, 60)

      this.ctx.fillStyle = '#FFFFFF'
      this.ctx.font = 'bold 18px Arial'
      this.ctx.textAlign = 'center'
      this.ctx.textBaseline = 'middle'
      this.ctx.fillText(`第${this.currentWave}波即将开始`, centerX, centerY)
    }
  }
  
  /**
   * 更新游戏逻辑
   */
  update(deltaTime) {
    if (this.isPaused) return

    // 更新动画时间
    this.animationTime += deltaTime * this.gameSpeed

    // 生成敌人
    this.spawnEnemies(deltaTime)

    // 更新敌人
    this.updateEnemies(deltaTime)

    // 更新塔攻击
    this.updateTowers(deltaTime)

    // 更新子弹
    this.updateBullets(deltaTime)

    // 检查游戏状态
    this.checkGameState()

    // 检查建造状态
    this.checkBuildState()
  }

  /**
   * 生成敌人
   */
  spawnEnemies(deltaTime) {
    if (this.gameState !== 'playing') return
    if (this.enemiesSpawned >= this.maxEnemiesPerWave) return

    const now = Date.now()
    if (now - this.lastEnemySpawnTime > this.enemySpawnInterval) {
      this.spawnEnemy()
      this.lastEnemySpawnTime = now
      this.enemiesSpawned++

      console.log(`生成敌人 ${this.enemiesSpawned}/${this.maxEnemiesPerWave}`)
    }
  }

  /**
   * 生成单个敌人
   */
  spawnEnemy() {
    // 根据波次增加敌人强度
    const waveMultiplier = 1 + (this.currentWave - 1) * 0.3

    const enemy = {
      id: Date.now(),
      x: this.path[0].x,
      y: this.path[0].y,
      hp: Math.floor(50 * waveMultiplier),
      maxHp: Math.floor(50 * waveMultiplier),
      speed: 1 + (this.currentWave - 1) * 0.1,
      pathIndex: 0,
      pathProgress: 0,
      reward: Math.floor(10 * waveMultiplier)
    }

    this.enemies.push(enemy)
  }

  /**
   * 更新敌人
   */
  updateEnemies(deltaTime) {
    for (let i = this.enemies.length - 1; i >= 0; i--) {
      const enemy = this.enemies[i]

      // 移动敌人
      this.moveEnemyAlongPath(enemy, deltaTime)

      // 检查是否到达终点
      if (enemy.pathIndex >= this.path.length - 1) {
        this.enemies.splice(i, 1)
        this.lives--
        console.log(`敌人到达终点，剩余生命: ${this.lives}`)
      }

      // 检查是否死亡
      if (enemy.hp <= 0) {
        this.enemies.splice(i, 1)
        this.gold += enemy.reward
        this.score += enemy.reward
        console.log(`击杀敌人，获得${enemy.reward}金币`)
      }
    }
  }

  /**
   * 移动敌人沿路径
   */
  moveEnemyAlongPath(enemy, deltaTime) {
    if (enemy.pathIndex >= this.path.length - 1) return

    const currentPoint = this.path[enemy.pathIndex]
    const nextPoint = this.path[enemy.pathIndex + 1]

    const dx = nextPoint.x - currentPoint.x
    const dy = nextPoint.y - currentPoint.y
    const distance = Math.sqrt(dx * dx + dy * dy)

    if (distance === 0) {
      enemy.pathIndex++
      return
    }

    const moveDistance = enemy.speed * deltaTime * this.gameSpeed / 16.67 // 假设60fps
    enemy.pathProgress += moveDistance / distance

    if (enemy.pathProgress >= 1) {
      enemy.pathProgress = 0
      enemy.pathIndex++
    }

    enemy.x = currentPoint.x + dx * enemy.pathProgress
    enemy.y = currentPoint.y + dy * enemy.pathProgress
  }

  /**
   * 更新塔
   */
  updateTowers(deltaTime) {
    const now = Date.now()

    for (const tower of this.towers) {
      if (now - tower.lastFireTime < tower.fireRate) continue

      // 寻找目标
      const target = this.findNearestEnemy(tower.x, tower.y, tower.range)
      if (target) {
        this.fireBullet(tower, target)
        tower.lastFireTime = now
      }
    }
  }

  /**
   * 寻找最近的敌人
   */
  findNearestEnemy(x, y, range) {
    let nearest = null
    let minDistance = range

    for (const enemy of this.enemies) {
      const distance = Math.sqrt((enemy.x - x) * (enemy.x - x) + (enemy.y - y) * (enemy.y - y))
      if (distance < minDistance) {
        minDistance = distance
        nearest = enemy
      }
    }

    return nearest
  }

  /**
   * 发射子弹
   */
  fireBullet(tower, target) {
    const dx = target.x - tower.x
    const dy = target.y - tower.y
    const distance = Math.sqrt(dx * dx + dy * dy)

    const bullet = {
      id: Date.now(),
      x: tower.x,
      y: tower.y,
      vx: (dx / distance) * 5, // 子弹速度
      vy: (dy / distance) * 5,
      damage: tower.damage,
      target: target
    }

    this.bullets.push(bullet)
  }

  /**
   * 更新子弹
   */
  updateBullets(deltaTime) {
    for (let i = this.bullets.length - 1; i >= 0; i--) {
      const bullet = this.bullets[i]

      // 移动子弹
      bullet.x += bullet.vx * deltaTime * this.gameSpeed / 16.67
      bullet.y += bullet.vy * deltaTime * this.gameSpeed / 16.67

      // 检查是否击中目标
      if (bullet.target && this.enemies.includes(bullet.target)) {
        const distance = Math.sqrt(
          (bullet.x - bullet.target.x) * (bullet.x - bullet.target.x) +
          (bullet.y - bullet.target.y) * (bullet.y - bullet.target.y)
        )

        if (distance < 20) {
          // 击中目标
          bullet.target.hp -= bullet.damage
          this.bullets.splice(i, 1)
          continue
        }
      }

      // 检查是否超出屏幕
      if (bullet.x < 0 || bullet.x > this.mapWidth ||
          bullet.y < 0 || bullet.y > this.mapHeight) {
        this.bullets.splice(i, 1)
      }
    }
  }

  /**
   * 检查游戏状态
   */
  checkGameState() {
    if (this.lives <= 0) {
      this.gameState = 'defeat'
      console.log('游戏失败')
      return
    }

    // 检查波次是否完成
    if (this.enemiesSpawned >= this.maxEnemiesPerWave && this.enemies.length === 0) {
      this.completeWave()
    }
  }

  /**
   * 完成当前波次
   */
  completeWave() {
    this.currentWave++
    this.enemiesSpawned = 0

    if (this.currentWave > this.totalWaves) {
      // 游戏胜利
      this.gameState = 'victory'
      console.log('游戏胜利！')
    } else {
      // 开始下一波
      console.log(`第${this.currentWave}波开始！`)
      // 给玩家一些奖励金币
      this.gold += 50
      // 稍微延迟开始下一波
      setTimeout(() => {
        if (this.gameState === 'playing') {
          this.lastEnemySpawnTime = Date.now()
        }
      }, 3000)
    }
  }

  /**
   * 检查建造状态
   */
  checkBuildState() {
    // 新的建造系统不需要持续检查状态
    // 塔选择菜单会在显示时检查金币
  }

  /**
   * 设置场景切换回调
   */
  setSceneChangeCallback(callback) {
    this.sceneChangeCallback = callback
  }

  /**
   * 销毁场景
   */
  destroy() {
    if (this.touchStartHandler) {
      wx.offTouchStart(this.touchStartHandler)
    }
    if (this.touchEndHandler) {
      wx.offTouchEnd(this.touchEndHandler)
    }
    if (this.touchMoveHandler) {
      wx.offTouchMove(this.touchMoveHandler)
    }

    // 停止背景音乐
    AudioManager.stopMusic()
  }
}

module.exports = BattleScene
