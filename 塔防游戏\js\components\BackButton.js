/**
 * 统一的返回按钮组件
 * 支持序列帧动画和静态备用方案
 */
class BackButton {
  constructor(imageManager) {
    this.imageManager = imageManager
    
    // 按钮配置
    this.config = {
      x: 45,      // 左上角位置
      y: 20,      // 左上角位置
      width: 80,  // 长方形宽度
      height: 36, // 长方形高度
      text: '←',  // 备用文字
      isPressed: false
    }
    
    // 序列帧相关
    this.frames = []
    this.frameIndex = 0
    this.frameTimer = 0
    this.frameInterval = 6  // 每6帧切换一次 (约100ms)
    
    // 初始化
    this.loadFrames()
  }
  
  /**
   * 加载序列帧
   */
  async loadFrames() {
    try {
      this.frames = await this.imageManager.loadBackButtonFrames()
      console.log(`返回按钮序列帧加载成功，共${this.frames.length}帧`)
    } catch (error) {
      console.warn('返回按钮序列帧加载失败:', error)
      this.frames = []
    }
  }
  
  /**
   * 更新动画
   */
  update() {
    if (this.frames.length === 0) return
    
    this.frameTimer++
    if (this.frameTimer >= this.frameInterval) {
      this.frameTimer = 0
      this.frameIndex++
      
      // 循环播放：8帧循环 (0-7)
      if (this.frameIndex >= 8) {
        this.frameIndex = 0
      }
    }
  }
  
  /**
   * 绘制返回按钮
   */
  draw(ctx) {
    const { x, y, width, height, text, isPressed } = this.config
    
    ctx.save()
    
    // 检查序列帧是否已加载
    if (this.frames && this.frames.length > 0) {
      // 使用序列帧绘制
      const safeIndex = this.frameIndex % Math.max(1, this.frames.length)
      const currentFrame = this.frames[safeIndex]
      
      if (currentFrame) {
        // 绘制序列帧图片（长方形）
        ctx.drawImage(
          currentFrame,
          x - width / 2, y - height / 2,  // 左上角位置
          width, height                   // 尺寸
        )
        
        // 如果按压，添加半透明遮罩（长方形）
        if (isPressed) {
          const radius = 8  // 长方形圆角
          ctx.fillStyle = 'rgba(0, 0, 0, 0.3)'
          this.drawRoundedRect(ctx, x - width / 2, y - height / 2, width, height, radius)
          ctx.fill()
        }
      } else {
        // 序列帧加载失败，使用备用绘制
        this.drawFallback(ctx)
      }
    } else {
      // 序列帧未加载，使用备用绘制
      this.drawFallback(ctx)
    }
    
    ctx.restore()
  }
  
  /**
   * 绘制备用方案（序列帧加载失败时使用）
   */
  drawFallback(ctx) {
    const { x, y, width, height, text, isPressed } = this.config
    const radius = 8  // 长方形圆角

    // 绘制长方形背景
    ctx.fillStyle = isPressed ? 'rgba(0, 0, 0, 0.8)' : 'rgba(0, 0, 0, 0.6)'
    this.drawRoundedRect(ctx, x - width / 2, y - height / 2, width, height, radius)
    ctx.fill()

    // 绘制长方形边框
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)'
    ctx.lineWidth = 1.5
    this.drawRoundedRect(ctx, x - width / 2, y - height / 2, width, height, radius)
    ctx.stroke()

    // 绘制箭头文字
    ctx.fillStyle = '#ffffff'
    ctx.font = 'bold 20px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText(text, x, y)
  }
  
  /**
   * 绘制圆角矩形
   */
  drawRoundedRect(ctx, x, y, width, height, radius) {
    ctx.beginPath()
    ctx.moveTo(x + radius, y)
    ctx.lineTo(x + width - radius, y)
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
    ctx.lineTo(x + width, y + height - radius)
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
    ctx.lineTo(x + radius, y + height)
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
    ctx.lineTo(x, y + radius)
    ctx.quadraticCurveTo(x, y, x + radius, y)
    ctx.closePath()
  }
  
  /**
   * 检查点击
   */
  isPointInButton(point) {
    const { x, y, width, height } = this.config
    return point.x >= x - width / 2 &&
           point.x <= x + width / 2 &&
           point.y >= y - height / 2 &&
           point.y <= y + height / 2
  }
  
  /**
   * 设置按压状态
   */
  setPressed(pressed) {
    this.config.isPressed = pressed
  }
  
  /**
   * 获取按压状态
   */
  isPressed() {
    return this.config.isPressed
  }
  
  /**
   * 渲染按钮
   */
  render(ctx) {
    const { x, y, width, height, text, isPressed } = this.config

    // 如果有序列帧，使用序列帧渲染
    if (this.frames.length > 0) {
      const frame = this.frames[this.frameIndex]
      if (frame && frame.complete) {
        ctx.drawImage(frame, x - width/2, y - height/2, width, height)
        return
      }
    }

    // 备用方案：绘制简单的返回按钮
    // 按钮背景
    ctx.fillStyle = isPressed ? 'rgba(100, 100, 100, 0.8)' : 'rgba(0, 0, 0, 0.7)'
    ctx.fillRect(x - width/2, y - height/2, width, height)

    // 按钮边框
    ctx.strokeStyle = '#FFFFFF'
    ctx.lineWidth = 2
    ctx.strokeRect(x - width/2, y - height/2, width, height)

    // 按钮文字
    ctx.fillStyle = '#FFFFFF'
    ctx.font = 'bold 20px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText(text, x, y)
  }

  /**
   * 获取按钮配置
   */
  getConfig() {
    return this.config
  }
}

// 导出组件
if (typeof module !== 'undefined' && module.exports) {
  module.exports = BackButton
}
