const ScreenAdapter = require('../utils/ScreenAdapter.js')
const BackButton = require('../components/BackButton.js')

/**
 * 塔页面场景
 */
class TowerScene {
  constructor(canvas, ctx) {
    this.canvas = canvas
    this.ctx = ctx
    this.adapter = new ScreenAdapter()

    // 设置Canvas
    this.adapter.setupCanvas(canvas)
    
    // 场景切换回调
    this.sceneChangeCallback = null
    
    // 塔数据
    this.towers = []
    this.selectedTower = null
    
    // 滚动相关
    this.scrollY = 0
    this.maxScrollY = 0
    this.isDragging = false
    this.lastTouchY = 0
    
    // 初始化塔数据
    this.initTowerData()

    // 更新解锁状态
    this.updateUnlockStatus()

    // 创建返回按钮
    this.createBackButton()
    
    // 绑定触摸事件
    this.bindEvents()
  }

  /**
   * 初始化塔数据
   */
  initTowerData() {
    this.towers = [
      {
        id: 1,
        name: '基础炮塔',
        icon: '🔫',
        type: 'basic',
        level: 1,
        maxLevel: 5,
        damage: 20,
        range: 80,
        fireRate: 1.0,
        cost: 50,
        description: '基础的防御塔，攻击力适中，射程一般',
        upgrades: [
          { level: 2, cost: 75, damage: 30, range: 90 },
          { level: 3, cost: 100, damage: 45, range: 100 },
          { level: 4, cost: 150, damage: 65, range: 110 },
          { level: 5, cost: 200, damage: 90, range: 120 }
        ],
        unlocked: true
      },
      {
        id: 2,
        name: '激光塔',
        icon: '⚡',
        type: 'laser',
        level: 1,
        maxLevel: 5,
        damage: 35,
        range: 100,
        fireRate: 0.8,
        cost: 80,
        description: '发射激光束，攻击力强，射程远',
        upgrades: [
          { level: 2, cost: 120, damage: 50, range: 110 },
          { level: 3, cost: 160, damage: 70, range: 120 },
          { level: 4, cost: 220, damage: 95, range: 130 },
          { level: 5, cost: 300, damage: 130, range: 140 }
        ],
        unlocked: true
      },
      {
        id: 3,
        name: '冰冻塔',
        icon: '❄️',
        type: 'ice',
        level: 1,
        maxLevel: 5,
        damage: 15,
        range: 70,
        fireRate: 1.2,
        cost: 60,
        description: '发射冰弹，能够减慢敌人移动速度',
        upgrades: [
          { level: 2, cost: 90, damage: 22, range: 80 },
          { level: 3, cost: 120, damage: 32, range: 90 },
          { level: 4, cost: 170, damage: 45, range: 100 },
          { level: 5, cost: 230, damage: 65, range: 110 }
        ],
        unlocked: false,
        unlockCondition: '完成关卡3解锁'
      },
      {
        id: 4,
        name: '火焰塔',
        icon: '🔥',
        type: 'fire',
        level: 1,
        maxLevel: 5,
        damage: 25,
        range: 60,
        fireRate: 1.5,
        cost: 70,
        description: '发射火球，造成范围伤害',
        upgrades: [
          { level: 2, cost: 105, damage: 38, range: 70 },
          { level: 3, cost: 140, damage: 55, range: 80 },
          { level: 4, cost: 190, damage: 75, range: 90 },
          { level: 5, cost: 260, damage: 105, range: 100 }
        ],
        unlocked: false,
        unlockCondition: '完成关卡5解锁'
      },
      {
        id: 5,
        name: '毒素塔',
        icon: '☠️',
        type: 'poison',
        level: 1,
        maxLevel: 5,
        damage: 18,
        range: 75,
        fireRate: 1.1,
        cost: 65,
        description: '发射毒弹，造成持续伤害',
        upgrades: [
          { level: 2, cost: 95, damage: 27, range: 85 },
          { level: 3, cost: 130, damage: 40, range: 95 },
          { level: 4, cost: 180, damage: 58, range: 105 },
          { level: 5, cost: 250, damage: 82, range: 115 }
        ],
        unlocked: false,
        unlockCondition: '完成关卡8解锁'
      },
      {
        id: 6,
        name: '雷电塔',
        icon: '⚡',
        type: 'thunder',
        level: 1,
        maxLevel: 5,
        damage: 40,
        range: 90,
        fireRate: 0.7,
        cost: 100,
        description: '发射闪电，可以链式攻击多个敌人',
        upgrades: [
          { level: 2, cost: 150, damage: 60, range: 100 },
          { level: 3, cost: 200, damage: 85, range: 110 },
          { level: 4, cost: 280, damage: 120, range: 120 },
          { level: 5, cost: 380, damage: 165, range: 130 }
        ],
        unlocked: false,
        unlockCondition: '完成关卡10解锁'
      }
    ]
    
    // 计算最大滚动距离
    this.calculateMaxScroll()
  }

  /**
   * 计算最大滚动距离
   */
  calculateMaxScroll() {
    const screenHeight = this.adapter.getWindowHeight()
    const headerHeight = 80
    const towerCardHeight = 120
    const towerSpacing = 10
    const totalContentHeight = headerHeight + (this.towers.length * (towerCardHeight + towerSpacing)) + 50

    this.maxScrollY = Math.max(0, totalContentHeight - screenHeight)
  }

  /**
   * 更新解锁状态
   */
  updateUnlockStatus() {
    try {
      const DataManager = require('../utils/DataManager.js')
      const levelProgress = DataManager.getLevelProgress()

      // 根据完成的关卡数量解锁塔
      const completedLevels = Object.keys(levelProgress.levelStars || {}).length

      this.towers.forEach(tower => {
        switch (tower.id) {
          case 1: // 基础炮塔
          case 2: // 激光塔
            tower.unlocked = true
            break
          case 3: // 冰冻塔
            tower.unlocked = completedLevels >= 3
            break
          case 4: // 火焰塔
            tower.unlocked = completedLevels >= 5
            break
          case 5: // 毒素塔
            tower.unlocked = completedLevels >= 8
            break
          case 6: // 雷电塔
            tower.unlocked = completedLevels >= 10
            break
        }
      })
    } catch (error) {
      console.warn('更新塔解锁状态失败:', error)
      // 如果出错，至少解锁前两个塔
      this.towers[0].unlocked = true
      this.towers[1].unlocked = true
    }
  }

  /**
   * 创建返回按钮
   */
  createBackButton() {
    this.backButtonComponent = {
      x: 20,
      y: 20,
      width: 60,
      height: 40,
      pressed: false,
      isPointInButton: function(pos) {
        return pos.x >= this.x && pos.x <= this.x + this.width &&
               pos.y >= this.y && pos.y <= this.y + this.height
      },
      setPressed: function(pressed) {
        this.pressed = pressed
      },
      isPressed: function() {
        return this.pressed
      },
      update: function(deltaTime) {
        // 空的更新方法，用于兼容其他场景的接口
        // 如果需要动画效果，可以在这里添加
      }
    }
  }

  /**
   * 返回按钮点击处理
   */
  onBackClick() {
    if (this.sceneChangeCallback) {
      this.sceneChangeCallback('game')
    }
  }

  /**
   * 设置场景切换回调
   */
  setSceneChangeCallback(callback) {
    this.sceneChangeCallback = callback
  }

  /**
   * 销毁场景
   */
  destroy() {
    // 清理事件监听器
    if (this.touchStartHandler) {
      wx.offTouchStart(this.touchStartHandler)
    }
    if (this.touchEndHandler) {
      wx.offTouchEnd(this.touchEndHandler)
    }
    if (this.touchMoveHandler) {
      wx.offTouchMove(this.touchMoveHandler)
    }
  }

  /**
   * 绑定触摸事件
   */
  bindEvents() {
    // 保存事件处理器的引用，以便后续清理
    this.touchStartHandler = this.onTouchStart.bind(this)
    this.touchEndHandler = this.onTouchEnd.bind(this)
    this.touchMoveHandler = this.onTouchMove.bind(this)

    wx.onTouchStart(this.touchStartHandler)
    wx.onTouchEnd(this.touchEndHandler)
    wx.onTouchMove(this.touchMoveHandler)
  }

  /**
   * 触摸开始事件
   */
  onTouchStart(e) {
    const touch = e.touches[0]
    const pos = { x: touch.pageX, y: touch.pageY }

    // 检查返回按钮
    if (this.backButtonComponent.isPointInButton(pos)) {
      this.backButtonComponent.setPressed(true)
      return
    }

    // 开始拖拽
    this.isDragging = true
    this.lastTouchY = pos.y
  }

  /**
   * 触摸移动事件
   */
  onTouchMove(e) {
    if (!this.isDragging) return
    
    const touch = e.touches[0]
    const currentY = touch.pageY
    const deltaY = currentY - this.lastTouchY
    
    // 更新滚动位置
    this.scrollY = Math.max(0, Math.min(this.maxScrollY, this.scrollY - deltaY))
    this.lastTouchY = currentY
  }

  /**
   * 触摸结束事件
   */
  onTouchEnd(e) {
    const touch = e.changedTouches[0]
    const pos = { x: touch.pageX, y: touch.pageY }

    if (!this.isDragging) {
      // 检查返回按钮点击
      if (this.backButtonComponent.isPressed() && this.backButtonComponent.isPointInButton(pos)) {
        this.onBackClick()
        this.resetButtonStates()
        return
      }

      // 检查塔卡片点击
      this.checkTowerCardClick(pos)
    }

    // 重置按钮状态
    this.resetButtonStates()

    // 结束拖拽
    this.isDragging = false
  }

  /**
   * 重置按钮状态
   */
  resetButtonStates() {
    this.backButtonComponent.setPressed(false)
  }

  /**
   * 检查塔卡片点击
   */
  checkTowerCardClick(pos) {
    const screenWidth = this.adapter.getWindowWidth()
    const cardWidth = screenWidth - 40
    const cardHeight = 120
    const cardX = 20
    let cardY = 100 - this.scrollY

    this.towers.forEach((tower, index) => {
      const currentCardY = cardY + index * (cardHeight + 10)

      if (pos.x >= cardX && pos.x <= cardX + cardWidth &&
          pos.y >= currentCardY && pos.y <= currentCardY + cardHeight) {

        // 检查是否点击了升级按钮
        if (tower.unlocked && tower.level < tower.maxLevel) {
          const buttonX = cardX + cardWidth - 80
          const buttonY = currentCardY + cardHeight - 35
          const buttonWidth = 60
          const buttonHeight = 25

          if (pos.x >= buttonX && pos.x <= buttonX + buttonWidth &&
              pos.y >= buttonY && pos.y <= buttonY + buttonHeight) {
            this.onUpgradeButtonClick(tower)
            return
          }
        }

        this.onTowerCardClick(tower)
      }
    })
  }

  /**
   * 塔卡片点击处理
   */
  onTowerCardClick(tower) {
    if (!tower.unlocked) {
      // 显示解锁条件
      this.showMessage(`${tower.name}尚未解锁`, tower.unlockCondition)
      return
    }

    this.selectedTower = tower
    this.showTowerDetails(tower)
  }

  /**
   * 升级按钮点击处理
   */
  onUpgradeButtonClick(tower) {
    if (tower.level >= tower.maxLevel) {
      this.showMessage('已达最高等级', `${tower.name}已经是最高等级`)
      return
    }

    const nextUpgrade = tower.upgrades[tower.level - 1]
    if (!nextUpgrade) {
      this.showMessage('升级数据错误', '找不到升级信息')
      return
    }

    // 这里可以检查玩家是否有足够的资源
    // 暂时直接升级
    this.upgradeTower(tower, nextUpgrade)
  }

  /**
   * 升级塔
   */
  upgradeTower(tower, upgrade) {
    tower.level = upgrade.level
    tower.damage = upgrade.damage
    tower.range = upgrade.range
    tower.cost = upgrade.cost

    this.showMessage('升级成功', `${tower.name}已升级到Lv.${tower.level}`)

    // 如果当前选中的是这个塔，更新选中状态
    if (this.selectedTower && this.selectedTower.id === tower.id) {
      this.selectedTower = tower
    }
  }

  /**
   * 显示塔详细信息
   */
  showTowerDetails(tower) {
    let details = `${tower.name} (Lv.${tower.level})\n\n`
    details += `攻击力: ${tower.damage}\n`
    details += `射程: ${tower.range}\n`
    details += `攻击速度: ${tower.fireRate.toFixed(1)}/秒\n`
    details += `建造费用: ${tower.cost}金币\n\n`
    details += `描述: ${tower.description}`

    if (tower.level < tower.maxLevel) {
      const nextUpgrade = tower.upgrades[tower.level - 1]
      details += `\n\n下一级:\n`
      details += `攻击力: ${nextUpgrade.damage} (+${nextUpgrade.damage - tower.damage})\n`
      details += `射程: ${nextUpgrade.range} (+${nextUpgrade.range - tower.range})\n`
      details += `升级费用: ${nextUpgrade.cost}金币`
    }

    this.showMessage(tower.name, details)
  }

  /**
   * 显示消息
   */
  showMessage(title, message) {
    // 在浏览器环境中使用alert，在微信小游戏中可以使用wx.showModal
    if (typeof wx !== 'undefined' && wx.showModal) {
      wx.showModal({
        title: title,
        content: message,
        showCancel: false
      })
    } else {
      alert(`${title}\n\n${message}`)
    }
  }

  /**
   * 更新场景
   */
  update(deltaTime) {
    // 更新返回按钮动画（如果有的话）
    if (this.backButtonComponent && this.backButtonComponent.update) {
      this.backButtonComponent.update(deltaTime)
    }

    // 这里可以添加其他需要更新的逻辑
    // 比如动画效果、粒子系统等
  }

  /**
   * 渲染场景
   */
  render() {
    this.ctx.save()
    
    // 清空画布
    this.ctx.fillStyle = '#1a1a2e'
    this.ctx.fillRect(0, 0, this.adapter.getWindowWidth(), this.adapter.getWindowHeight())
    
    // 绘制背景
    this.drawBackground()
    
    // 绘制标题
    this.drawTitle()
    
    // 绘制塔列表
    this.drawTowerList()
    
    // 绘制返回按钮
    this.drawBackButton()
    
    this.ctx.restore()
  }

  /**
   * 绘制背景
   */
  drawBackground() {
    const gradient = this.ctx.createLinearGradient(0, 0, 0, this.adapter.getWindowHeight())
    gradient.addColorStop(0, '#1a1a2e')
    gradient.addColorStop(1, '#16213e')
    
    this.ctx.fillStyle = gradient
    this.ctx.fillRect(0, 0, this.adapter.getWindowWidth(), this.adapter.getWindowHeight())
  }

  /**
   * 绘制标题
   */
  drawTitle() {
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = 'bold 28px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText('防御塔图鉴', this.adapter.getWindowWidth() / 2, 50)
  }

  /**
   * 绘制塔列表
   */
  drawTowerList() {
    const screenWidth = this.adapter.getWindowWidth()
    const cardWidth = screenWidth - 40
    const cardHeight = 120
    const cardX = 20
    let cardY = 100 - this.scrollY

    this.towers.forEach((tower, index) => {
      const currentCardY = cardY + index * (cardHeight + 10)

      // 只绘制在屏幕范围内的卡片
      if (currentCardY + cardHeight >= 0 && currentCardY <= this.adapter.getWindowHeight()) {
        this.drawTowerCard(tower, cardX, currentCardY, cardWidth, cardHeight)
      }
    })
  }

  /**
   * 绘制塔卡片
   */
  drawTowerCard(tower, x, y, width, height) {
    // 卡片背景
    const isSelected = this.selectedTower && this.selectedTower.id === tower.id
    const isUnlocked = tower.unlocked

    this.ctx.fillStyle = isSelected ? 'rgba(255, 215, 0, 0.3)' :
                        isUnlocked ? 'rgba(255, 255, 255, 0.1)' : 'rgba(100, 100, 100, 0.1)'
    this.ctx.fillRect(x, y, width, height)

    // 卡片边框
    this.ctx.strokeStyle = isSelected ? '#FFD700' :
                          isUnlocked ? 'rgba(255, 255, 255, 0.3)' : 'rgba(100, 100, 100, 0.3)'
    this.ctx.lineWidth = isSelected ? 3 : 2
    this.ctx.strokeRect(x, y, width, height)

    // 如果未解锁，添加遮罩
    if (!isUnlocked) {
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.6)'
      this.ctx.fillRect(x, y, width, height)
    }

    // 塔图标
    this.ctx.font = '32px Arial'
    this.ctx.textAlign = 'left'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillStyle = isUnlocked ? '#ffffff' : '#666666'
    this.ctx.fillText(tower.icon, x + 15, y + 35)

    // 塔名称
    this.ctx.font = 'bold 18px Arial'
    this.ctx.fillStyle = isUnlocked ? '#ffffff' : '#888888'
    this.ctx.fillText(tower.name, x + 70, y + 25)

    // 等级显示
    if (isUnlocked) {
      this.ctx.font = '14px Arial'
      this.ctx.fillStyle = '#FFD700'
      this.ctx.fillText(`Lv.${tower.level}/${tower.maxLevel}`, x + 70, y + 45)
    }

    // 属性信息
    if (isUnlocked) {
      this.ctx.font = '12px Arial'
      this.ctx.fillStyle = '#cccccc'
      this.ctx.fillText(`攻击: ${tower.damage}`, x + 15, y + 70)
      this.ctx.fillText(`射程: ${tower.range}`, x + 100, y + 70)
      this.ctx.fillText(`费用: ${tower.cost}`, x + 180, y + 70)
    }

    // 描述文字
    this.ctx.font = '11px Arial'
    this.ctx.fillStyle = isUnlocked ? '#aaaaaa' : '#666666'
    const description = isUnlocked ? tower.description : tower.unlockCondition
    this.drawWrappedText(description, x + 15, y + 90, width - 30, 12)

    // 升级按钮（仅解锁且未满级时显示）
    if (isUnlocked && tower.level < tower.maxLevel) {
      const buttonX = x + width - 80
      const buttonY = y + height - 35
      const buttonWidth = 60
      const buttonHeight = 25

      this.ctx.fillStyle = 'rgba(0, 150, 255, 0.8)'
      this.ctx.fillRect(buttonX, buttonY, buttonWidth, buttonHeight)

      this.ctx.strokeStyle = '#0096ff'
      this.ctx.lineWidth = 1
      this.ctx.strokeRect(buttonX, buttonY, buttonWidth, buttonHeight)

      this.ctx.font = '12px Arial'
      this.ctx.fillStyle = '#ffffff'
      this.ctx.textAlign = 'center'
      this.ctx.fillText('升级', buttonX + buttonWidth/2, buttonY + buttonHeight/2 + 2)
    }
  }

  /**
   * 绘制换行文本
   */
  drawWrappedText(text, x, y, maxWidth, lineHeight) {
    const words = text.split('')
    let line = ''
    let currentY = y

    for (let i = 0; i < words.length; i++) {
      const testLine = line + words[i]
      const metrics = this.ctx.measureText(testLine)

      if (metrics.width > maxWidth && line !== '') {
        this.ctx.fillText(line, x, currentY)
        line = words[i]
        currentY += lineHeight
      } else {
        line = testLine
      }
    }

    if (line !== '') {
      this.ctx.fillText(line, x, currentY)
    }
  }

  /**
   * 绘制返回按钮
   */
  drawBackButton() {
    if (!this.backButtonComponent) return

    // 获取按钮位置和状态
    const buttonX = 20
    const buttonY = 20
    const buttonWidth = 60
    const buttonHeight = 40
    const isPressed = this.backButtonComponent.isPressed()

    // 更新按钮位置（如果需要）
    this.backButtonComponent.x = buttonX
    this.backButtonComponent.y = buttonY
    this.backButtonComponent.width = buttonWidth
    this.backButtonComponent.height = buttonHeight

    // 绘制按钮背景
    this.ctx.fillStyle = isPressed ? 'rgba(255, 255, 255, 0.3)' : 'rgba(255, 255, 255, 0.2)'
    this.ctx.fillRect(buttonX, buttonY, buttonWidth, buttonHeight)

    // 绘制按钮边框
    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)'
    this.ctx.lineWidth = 2
    this.ctx.strokeRect(buttonX, buttonY, buttonWidth, buttonHeight)

    // 绘制返回箭头
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = '20px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText('←', buttonX + buttonWidth/2, buttonY + buttonHeight/2)
  }
}

module.exports = TowerScene
