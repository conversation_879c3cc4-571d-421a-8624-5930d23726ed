const ScreenAdapter = require('../utils/ScreenAdapter.js')

/**
 * 游戏场景
 */
class GameScene {
  constructor(canvas, ctx) {
    this.canvas = canvas
    this.ctx = ctx
    this.adapter = new ScreenAdapter()

    // 不重复设置Canvas，main.js已经设置过了
    // this.adapter.setupCanvas(canvas)
    
    // 游戏状态
    this.gameTime = 0
    this.score = 0

    // 导航栏状态
    this.navVisible = true  // 导航栏是否可见
    this.navAnimating = false  // 是否正在动画中
    this.navAnimationProgress = 1  // 动画进度 (0-1)

    // 设置导航栏
    this.setupNavigation()

    // 绑定触摸事件
    this.bindEvents()
  }
  
  /**
   * 设置导航栏 - 底部靠右水平布局
   */
  setupNavigation() {
    const windowWidth = this.adapter.getWindowWidth()
    const windowHeight = this.adapter.getWindowHeight()
    const safeRight = Math.min(windowWidth - 10, this.adapter.getSafeAreaRight())
    const safeBottom = Math.min(windowHeight - 10, this.adapter.getSafeAreaBottom())

    // 导航栏配置
    const buttonWidth = 50
    const buttonHeight = 50
    const buttonSpacing = 55  // 按钮之间的水平间距
    const marginRight = 15    // 距离右边缘的距离
    const marginBottom = 10   // 距离底部的距离（减少预留空间）

    // 计算起始位置（从右往左排列）
    const startY = safeBottom - marginBottom - buttonHeight / 2
    const startX = safeRight - marginRight - buttonWidth / 2

    // 导航栏切换按钮（始终可见）- 垂直长方形设计
    this.navToggleButton = {
      text: '',  // 不显示文字
      icon: '◀',  // 使用三角形图标
      x: startX + 25,  // 稍微向右偏移
      y: startY,
      width: 20,   // 较窄的宽度
      height: 60,  // 较高的高度，垂直长方形
      isPressed: false
    }

    // 导航按钮 - 水平排列，从右往左（可隐藏）
    // 紧贴切换按钮，减少间距
    const navStartX = startX - 30  // 减少与切换按钮的距离

    this.navButtons = {
      skill: {
        text: '技能',
        icon: '✨',
        x: navStartX,
        y: startY,
        width: buttonWidth,
        height: buttonHeight,
        isPressed: false,
        baseX: navStartX
      },
      bag: {
        text: '背包',
        icon: '🎒',
        x: navStartX - buttonSpacing,
        y: startY,
        width: buttonWidth,
        height: buttonHeight,
        isPressed: false,
        baseX: navStartX - buttonSpacing
      },
      hero: {
        text: '英雄',
        icon: '🛡️',
        x: navStartX - buttonSpacing * 2,
        y: startY,
        width: buttonWidth,
        height: buttonHeight,
        isPressed: false,
        baseX: navStartX - buttonSpacing * 2
      },
      challenge: {
        text: '挑战',
        icon: '⚔️',
        x: navStartX - buttonSpacing * 3,
        y: startY,
        width: buttonWidth,
        height: buttonHeight,
        isPressed: false,
        baseX: navStartX - buttonSpacing * 3
      }
    }


  }

  /**
   * 绑定触摸事件
   */
  bindEvents() {
    // 保存事件处理器的引用，以便后续清理
    this.touchStartHandler = this.onTouchStart.bind(this)
    this.touchEndHandler = this.onTouchEnd.bind(this)

    wx.onTouchStart(this.touchStartHandler)
    wx.onTouchEnd(this.touchEndHandler)
  }

  /**
   * 触摸开始事件
   */
  onTouchStart(e) {
    const touch = e.touches[0]
    const pos = { x: touch.pageX, y: touch.pageY }

    // 检查切换按钮点击
    if (this.isPointInButton(pos, this.navToggleButton)) {
      this.navToggleButton.isPressed = true
    }

    // 检查导航按钮点击（只有在可见时）
    if (this.navVisible) {
      Object.keys(this.navButtons).forEach(key => {
        const button = this.navButtons[key]
        if (this.isPointInButton(pos, button)) {
          button.isPressed = true
        }
      })
    }
  }

  /**
   * 触摸结束事件
   */
  onTouchEnd(e) {
    const touch = e.changedTouches[0]
    const pos = { x: touch.pageX, y: touch.pageY }

    // 检查切换按钮点击
    if (this.navToggleButton.isPressed && this.isPointInButton(pos, this.navToggleButton)) {
      this.toggleNavigation()
    }
    this.navToggleButton.isPressed = false

    // 检查导航按钮点击（只有在可见时）
    if (this.navVisible) {
      Object.keys(this.navButtons).forEach(key => {
        const button = this.navButtons[key]
        if (button.isPressed && this.isPointInButton(pos, button)) {
          this.onNavButtonClick(key)
        }
        button.isPressed = false
      })
    }
  }

  /**
   * 检查点是否在按钮内
   */
  isPointInButton(pos, button) {
    return pos.x >= button.x - button.width / 2 &&
           pos.x <= button.x + button.width / 2 &&
           pos.y >= button.y - button.height / 2 &&
           pos.y <= button.y + button.height / 2
  }

  /**
   * 切换导航栏显示/隐藏
   */
  toggleNavigation() {
    if (this.navAnimating) return  // 如果正在动画中，忽略点击

    this.navVisible = !this.navVisible
    this.navAnimating = true

    // 开始动画
    this.startNavAnimation()
  }

  /**
   * 开始导航栏动画
   */
  startNavAnimation() {
    const animationDuration = 300  // 动画持续时间（毫秒）
    const startTime = Date.now()
    const startProgress = this.navAnimationProgress
    const targetProgress = this.navVisible ? 1 : 0

    const animate = () => {
      const elapsed = Date.now() - startTime
      const progress = Math.min(elapsed / animationDuration, 1)

      // 使用缓动函数
      const easeProgress = this.easeInOutCubic(progress)
      this.navAnimationProgress = startProgress + (targetProgress - startProgress) * easeProgress

      // 更新按钮位置
      this.updateNavButtonPositions()

      if (progress < 1) {
        requestAnimationFrame(animate)
      } else {
        this.navAnimating = false
        this.navAnimationProgress = targetProgress
      }
    }

    animate()
  }

  /**
   * 缓动函数
   */
  easeInOutCubic(t) {
    return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1
  }

  /**
   * 更新导航按钮位置
   */
  updateNavButtonPositions() {
    const windowWidth = this.adapter.getWindowWidth()
    const hideOffset = 200  // 隐藏时向右偏移的距离

    Object.keys(this.navButtons).forEach(key => {
      const button = this.navButtons[key]
      const targetX = button.baseX + hideOffset * (1 - this.navAnimationProgress)
      button.x = targetX
    })
  }

  /**
   * 导航按钮点击处理
   */
  onNavButtonClick(buttonKey) {
    switch (buttonKey) {
      case 'challenge':
        if (this.onSceneChange) {
          this.onSceneChange('challenge')
        }
        break
      case 'hero':
        if (this.onSceneChange) {
          this.onSceneChange('hero')
        }
        break
      case 'bag':
        wx.showToast({
          title: '背包功能开发中',
          icon: 'none'
        })
        break
      case 'skill':
        wx.showToast({
          title: '技能功能开发中',
          icon: 'none'
        })
        break
    }
  }

  /**
   * 设置场景切换回调
   */
  setSceneChangeCallback(callback) {
    this.onSceneChange = callback
  }

  /**
   * 更新游戏逻辑
   */
  update(deltaTime) {
    this.gameTime += deltaTime
    this.score = Math.floor(this.gameTime / 1000)
  }
  
  /**
   * 渲染场景
   */
  render() {
    // 应用像素比缩放
    this.ctx.save()
    this.ctx.scale(this.adapter.getPixelRatio(), this.adapter.getPixelRatio())

    // 绘制背景
    this.drawBackground()

    // 绘制游戏内容
    this.drawGameContent()

    // 绘制导航栏
    this.drawNavigation()

    this.ctx.restore()
  }
  
  /**
   * 绘制背景
   */
  drawBackground() {
    this.ctx.fillStyle = '#2c3e50'
    this.ctx.fillRect(0, 0, this.adapter.getWindowWidth(), this.adapter.getWindowHeight())
  }
  

  
  /**
   * 绘制游戏内容
   */
  drawGameContent() {
    this.ctx.fillStyle = '#3498db'
    this.ctx.font = '24px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'

    const centerX = this.adapter.getWindowWidth() / 2
    const centerY = this.adapter.getWindowHeight() / 2 - 40  // 向上偏移，为导航栏留空间

    this.ctx.fillText('塔防游戏区域', centerX, centerY - 30)
    this.ctx.fillText('(游戏逻辑待实现)', centerX, centerY + 30)
  }

  /**
   * 绘制导航栏 - 底部靠右水平布局
   */
  drawNavigation() {
    // 绘制切换按钮（始终可见）
    this.drawToggleButton(this.navToggleButton)

    // 绘制导航按钮（根据动画进度显示）
    if (this.navAnimationProgress > 0) {
      this.ctx.save()
      this.ctx.globalAlpha = this.navAnimationProgress

      Object.values(this.navButtons).forEach(button => {
        this.drawNavButton(button)
      })

      this.ctx.restore()
    }
  }

  /**
   * 绘制圆角矩形
   */
  drawRoundedRect(x, y, width, height, radius) {
    this.ctx.beginPath()
    this.ctx.moveTo(x + radius, y)
    this.ctx.lineTo(x + width - radius, y)
    this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
    this.ctx.lineTo(x + width, y + height - radius)
    this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
    this.ctx.lineTo(x + radius, y + height)
    this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
    this.ctx.lineTo(x, y + radius)
    this.ctx.quadraticCurveTo(x, y, x + radius, y)
    this.ctx.closePath()
  }

  /**
   * 绘制切换按钮 - 垂直长方形设计
   */
  drawToggleButton(button) {
    const { x, y, width, height, icon, isPressed } = button

    const rectX = x - width / 2
    const rectY = y - height / 2
    const radius = 10  // 较小的圆角

    // 按钮阴影效果
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.4)'
    this.drawRoundedRect(rectX + 1, rectY + 1, width, height, radius)
    this.ctx.fill()

    // 按钮背景 - 渐变效果
    const gradient = this.ctx.createLinearGradient(rectX, rectY, rectX + width, rectY)
    if (isPressed) {
      gradient.addColorStop(0, 'rgba(231, 76, 60, 0.95)')
      gradient.addColorStop(1, 'rgba(192, 57, 43, 0.95)')
    } else {
      gradient.addColorStop(0, 'rgba(52, 73, 94, 0.95)')
      gradient.addColorStop(1, 'rgba(44, 62, 80, 0.95)')
    }

    this.ctx.fillStyle = gradient
    this.drawRoundedRect(rectX, rectY, width, height, radius)
    this.ctx.fill()

    // 按钮边框
    this.ctx.strokeStyle = isPressed ? '#ffffff' : 'rgba(255, 255, 255, 0.8)'
    this.ctx.lineWidth = 1
    this.drawRoundedRect(rectX, rectY, width, height, radius)
    this.ctx.stroke()

    // 绘制大三角形图标
    this.drawTriangleIcon(x, y, isPressed)
  }

  /**
   * 绘制三角形图标
   */
  drawTriangleIcon(x, y, isPressed) {
    const size = 8  // 三角形大小
    const direction = this.navVisible ? -1 : 1  // 根据导航栏状态改变方向

    this.ctx.fillStyle = isPressed ? '#ffffff' : 'rgba(255, 255, 255, 0.9)'
    this.ctx.beginPath()

    if (direction === -1) {
      // 向左的三角形（隐藏导航栏）
      this.ctx.moveTo(x + size, y - size)
      this.ctx.lineTo(x - size, y)
      this.ctx.lineTo(x + size, y + size)
    } else {
      // 向右的三角形（显示导航栏）
      this.ctx.moveTo(x - size, y - size)
      this.ctx.lineTo(x + size, y)
      this.ctx.lineTo(x - size, y + size)
    }

    this.ctx.closePath()
    this.ctx.fill()
  }

  /**
   * 绘制导航按钮 - 底部靠右样式
   */
  drawNavButton(button) {
    const { x, y, width, height, text, icon, isPressed } = button

    const rectX = x - width / 2
    const rectY = y - height / 2
    const radius = 25  // 更大的圆角，接近圆形

    // 按钮阴影效果
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)'
    this.drawRoundedRect(rectX + 2, rectY + 2, width, height, radius)
    this.ctx.fill()

    // 按钮背景 - 使用圆角矩形
    this.ctx.fillStyle = isPressed ? 'rgba(231, 76, 60, 0.9)' : 'rgba(52, 152, 219, 0.9)'
    this.drawRoundedRect(rectX, rectY, width, height, radius)
    this.ctx.fill()

    // 按钮边框
    this.ctx.strokeStyle = isPressed ? '#ffffff' : 'rgba(255, 255, 255, 0.9)'
    this.ctx.lineWidth = 2
    this.drawRoundedRect(rectX, rectY, width, height, radius)
    this.ctx.stroke()

    // 绘制图标 - 稍大一些
    this.ctx.fillStyle = '#ffffff'
    this.ctx.font = '20px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.textBaseline = 'middle'
    this.ctx.fillText(icon, x, y - 2)

    // 绘制文字 - 更小的字体
    this.ctx.font = 'bold 10px Arial'
    this.ctx.fillText(text, x, y + 12)
  }
  

  
  /**
   * 销毁场景
   */
  destroy() {
    // 清理触摸事件监听器
    if (this.touchStartHandler) {
      wx.offTouchStart(this.touchStartHandler)
    }
    if (this.touchEndHandler) {
      wx.offTouchEnd(this.touchEndHandler)
    }

    // 清理其他资源
    this.touchStartHandler = null
    this.touchEndHandler = null
  }
}

// 导出模块
module.exports = GameScene
