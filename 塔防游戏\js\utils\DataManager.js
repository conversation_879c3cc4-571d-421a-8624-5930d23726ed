/**
 * 数据管理器
 * 处理游戏数据的保存和加载
 */

class DataManager {
  constructor() {
    // 数据键名
    this.keys = {
      PLAYER_DATA: 'td_player_data',
      LEVEL_PROGRESS: 'td_level_progress',
      SETTINGS: 'td_settings',
      STATISTICS: 'td_statistics'
    }
    
    // 默认数据
    this.defaultPlayerData = {
      name: '玩家',
      level: 1,
      exp: 0,
      gold: 500,
      gems: 0,
      totalPlayTime: 0,
      lastLoginTime: Date.now(),
      createdTime: Date.now()
    }
    
    this.defaultLevelProgress = {
      unlockedLevels: [1], // 默认解锁第一关
      levelStars: {},      // 关卡星级 {levelId: stars}
      levelBestTimes: {},  // 关卡最佳时间
      levelCompletions: {} // 关卡完成次数
    }
    
    this.defaultSettings = {
      musicEnabled: true,
      sfxEnabled: true,
      musicVolume: 0.7,
      sfxVolume: 0.8,
      language: 'zh-CN',
      showFPS: false,
      autoSave: true
    }
    
    this.defaultStatistics = {
      totalGamesPlayed: 0,
      totalEnemiesKilled: 0,
      totalTowersBuilt: 0,
      totalGoldEarned: 0,
      totalPlayTime: 0,
      favoriteLevel: 1,
      highestWave: 1
    }
    
    // 缓存数据
    this.playerData = null
    this.levelProgress = null
    this.settings = null
    this.statistics = null
    
    // 自动保存
    this.autoSaveInterval = null
    this.isDirty = false
    
    // 初始化
    this.initialize()
  }
  
  /**
   * 初始化数据管理器
   */
  initialize() {
    try {
      // 清理损坏的数据
      this.cleanCorruptedData()

      // 加载所有数据
      this.loadAllData()

      // 启动自动保存
      this.startAutoSave()

      // 测试存储功能
      this.testStorage()

      // console.log('数据管理器初始化成功')
    } catch (error) {
      console.error('数据管理器初始化失败:', error)
    }
  }

  /**
   * 清理损坏的数据
   */
  cleanCorruptedData() {
    if (typeof localStorage === 'undefined') return

    const keys = Object.values(this.keys)
    keys.forEach(key => {
      try {
        const data = localStorage.getItem(key)
        if (data) {
          JSON.parse(data) // 尝试解析
        }
      } catch (error) {
        console.warn(`发现损坏的数据，正在清理: ${key}`)
        localStorage.removeItem(key)
      }
    })
  }

  /**
   * 测试存储功能
   */
  testStorage() {
    const testKey = 'td_storage_test'
    const testData = { test: true, timestamp: Date.now() }

    // 测试写入
    const writeSuccess = this.setStorageSync(testKey, testData)
    if (!writeSuccess) {
      console.error('存储写入测试失败')
      return
    }

    // 测试读取
    const readData = this.getStorageSync(testKey)
    if (!readData || readData.test !== true) {
      console.error('存储读取测试失败')
      return
    }

    // console.log('存储功能测试通过')

    // 清理测试数据
    try {
      if (typeof localStorage !== 'undefined') {
        localStorage.removeItem(testKey)
      }
    } catch (e) {
      // 忽略清理错误
    }
  }
  
  /**
   * 加载所有数据
   */
  loadAllData() {
    this.playerData = this.loadData(this.keys.PLAYER_DATA, this.defaultPlayerData)
    this.levelProgress = this.loadData(this.keys.LEVEL_PROGRESS, this.defaultLevelProgress)
    this.settings = this.loadData(this.keys.SETTINGS, this.defaultSettings)
    this.statistics = this.loadData(this.keys.STATISTICS, this.defaultStatistics)
    
    // 更新最后登录时间
    this.playerData.lastLoginTime = Date.now()
    this.markDirty()
  }
  
  /**
   * 获取存储数据（跨平台适配）
   */
  getStorageSync(key) {
    try {
      // 微信小程序环境
      if (typeof wx !== 'undefined' && wx.getStorageSync) {
        return wx.getStorageSync(key)
      }
      // 浏览器环境
      else if (typeof localStorage !== 'undefined') {
        const data = localStorage.getItem(key)
        if (!data) return null

        try {
          return JSON.parse(data)
        } catch (parseError) {
          console.error(`JSON解析失败: ${key}`, parseError)
          console.error('损坏的数据:', data)

          // 清除损坏的数据
          localStorage.removeItem(key)
          console.log(`已清除损坏的数据: ${key}`)

          return null
        }
      }
      // Node.js环境或其他环境
      else {
        console.warn('没有可用的存储API')
        return null
      }
    } catch (error) {
      console.warn(`读取存储失败: ${key}`, error)
      return null
    }
  }

  /**
   * 设置存储数据（跨平台适配）
   */
  setStorageSync(key, data) {
    try {
      // 微信小程序环境
      if (typeof wx !== 'undefined' && wx.setStorageSync) {
        wx.setStorageSync(key, data)
        return true
      }
      // 浏览器环境
      else if (typeof localStorage !== 'undefined') {
        console.log(`=== 存储数据到 ${key} ===`)
        console.log('原始数据:', data)
        console.log('数据类型:', typeof data)
        console.log('数据内容:', JSON.stringify(data))

        // 确保数据是对象而不是字符串
        let dataToStore = data
        if (typeof data === 'string') {
          console.warn(`警告: 尝试存储字符串数据到 ${key}, 这可能导致双重序列化`)
          try {
            // 尝试解析字符串，看是否已经是JSON
            dataToStore = JSON.parse(data)
          } catch (e) {
            // 如果解析失败，说明是普通字符串，直接存储
          }
        }

        const jsonString = JSON.stringify(dataToStore)
        console.log('序列化后的JSON字符串:', jsonString)
        console.log('JSON字符串长度:', jsonString.length)

        localStorage.setItem(key, jsonString)

        // 验证存储是否成功
        const stored = localStorage.getItem(key)
        console.log('存储后读取的内容:', stored)
        console.log('存储验证:', stored === jsonString ? '成功' : '失败')

        if (stored !== jsonString) {
          console.error(`存储验证失败: ${key}`)
          console.error('期望:', jsonString)
          console.error('实际:', stored)
          return false
        }

        return true
      }
      // Node.js环境或其他环境
      else {
        console.warn('没有可用的存储API')
        return false
      }
    } catch (error) {
      console.error(`写入存储失败: ${key}`, error)
      console.error('尝试存储的数据:', data)
      return false
    }
  }

  /**
   * 加载单个数据
   */
  loadData(key, defaultValue) {
    try {
      const data = this.getStorageSync(key)
      if (data) {
        // 合并默认值（处理新增字段）
        return { ...defaultValue, ...data }
      }
    } catch (error) {
      console.warn(`加载数据失败: ${key}`, error)
    }

    return { ...defaultValue }
  }
  
  /**
   * 保存单个数据
   */
  saveData(key, data) {
    return this.setStorageSync(key, data)
  }
  
  /**
   * 保存所有数据
   */
  saveAllData() {
    try {
      console.log('=== DataManager保存数据 ===')
      console.log('保存前 levelProgress:', this.levelProgress)

      const result1 = this.saveData(this.keys.PLAYER_DATA, this.playerData)
      const result2 = this.saveData(this.keys.LEVEL_PROGRESS, this.levelProgress)
      const result3 = this.saveData(this.keys.SETTINGS, this.settings)
      const result4 = this.saveData(this.keys.STATISTICS, this.statistics)

      console.log('保存结果:', {
        playerData: result1,
        levelProgress: result2,
        settings: result3,
        statistics: result4
      })

      // 验证保存结果
      if (typeof localStorage !== 'undefined') {
        const stored = localStorage.getItem(this.keys.LEVEL_PROGRESS)
        console.log('localStorage实际存储的内容:', stored)
        console.log('localStorage内容类型:', typeof stored)
        console.log('localStorage内容长度:', stored ? stored.length : 0)
      }

      this.isDirty = false
      // console.log('数据保存成功')
      return result1 && result2 && result3 && result4
    } catch (error) {
      console.error('数据保存失败:', error)
      return false
    }
  }
  
  /**
   * 标记数据为脏数据
   */
  markDirty() {
    this.isDirty = true
  }
  
  /**
   * 启动自动保存
   */
  startAutoSave() {
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval)
    }
    
    this.autoSaveInterval = setInterval(() => {
      if (this.isDirty && this.settings.autoSave) {
        this.saveAllData()
      }
    }, 30000) // 每30秒自动保存一次
  }
  
  /**
   * 停止自动保存
   */
  stopAutoSave() {
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval)
      this.autoSaveInterval = null
    }
  }
  
  // ==================== 玩家数据相关 ====================
  
  /**
   * 获取玩家数据
   */
  getPlayerData() {
    return { ...this.playerData }
  }
  
  /**
   * 更新玩家数据
   */
  updatePlayerData(updates) {
    Object.assign(this.playerData, updates)
    this.markDirty()
  }
  
  /**
   * 增加金币
   */
  addGold(amount) {
    this.playerData.gold += amount
    this.statistics.totalGoldEarned += amount
    this.markDirty()
  }
  
  /**
   * 消费金币
   */
  spendGold(amount) {
    if (this.playerData.gold >= amount) {
      this.playerData.gold -= amount
      this.markDirty()
      return true
    }
    return false
  }
  
  /**
   * 增加经验
   */
  addExp(amount) {
    this.playerData.exp += amount
    
    // 检查升级
    const newLevel = this.calculateLevel(this.playerData.exp)
    if (newLevel > this.playerData.level) {
      this.playerData.level = newLevel
      // 触发升级事件
      if (this.onLevelUp) {
        this.onLevelUp(newLevel)
      }
    }
    
    this.markDirty()
  }
  
  /**
   * 计算等级
   */
  calculateLevel(exp) {
    // 简单的等级计算公式
    return Math.floor(exp / 100) + 1
  }
  
  // ==================== 关卡进度相关 ====================
  
  /**
   * 获取关卡进度
   */
  getLevelProgress() {
    return { ...this.levelProgress }
  }
  
  /**
   * 检查关卡是否解锁
   */
  isLevelUnlocked(levelId) {
    return this.levelProgress.unlockedLevels.includes(levelId)
  }
  
  /**
   * 解锁关卡
   */
  unlockLevel(levelId) {
    if (!this.isLevelUnlocked(levelId)) {
      this.levelProgress.unlockedLevels.push(levelId)
      this.levelProgress.unlockedLevels.sort((a, b) => a - b)
      this.markDirty()
    }
  }
  
  /**
   * 完成关卡
   */
  completeLevel(levelId, stars, time) {
    // 更新星级（取最高值）
    const currentStars = this.levelProgress.levelStars[levelId] || 0
    this.levelProgress.levelStars[levelId] = Math.max(currentStars, stars)
    
    // 更新最佳时间（取最短时间）
    const currentBestTime = this.levelProgress.levelBestTimes[levelId]
    if (!currentBestTime || time < currentBestTime) {
      this.levelProgress.levelBestTimes[levelId] = time
    }
    
    // 更新完成次数
    this.levelProgress.levelCompletions[levelId] = (this.levelProgress.levelCompletions[levelId] || 0) + 1
    
    // 解锁下一关
    this.unlockLevel(levelId + 1)
    
    this.markDirty()
  }
  
  /**
   * 获取关卡星级
   */
  getLevelStars(levelId) {
    return this.levelProgress.levelStars[levelId] || 0
  }
  
  // ==================== 设置相关 ====================
  
  /**
   * 获取设置
   */
  getSettings() {
    return { ...this.settings }
  }
  
  /**
   * 更新设置
   */
  updateSettings(updates) {
    Object.assign(this.settings, updates)
    this.markDirty()
  }
  
  /**
   * 获取单个设置
   */
  getSetting(key) {
    return this.settings[key]
  }
  
  /**
   * 设置单个设置
   */
  setSetting(key, value) {
    this.settings[key] = value
    this.markDirty()
  }
  
  // ==================== 统计数据相关 ====================
  
  /**
   * 获取统计数据
   */
  getStatistics() {
    return { ...this.statistics }
  }
  
  /**
   * 更新统计数据
   */
  updateStatistics(updates) {
    Object.assign(this.statistics, updates)
    this.markDirty()
  }
  
  /**
   * 记录游戏开始
   */
  recordGameStart() {
    this.statistics.totalGamesPlayed++
    this.markDirty()
  }
  
  /**
   * 记录敌人击杀
   */
  recordEnemyKill(count = 1) {
    this.statistics.totalEnemiesKilled += count
    this.markDirty()
  }
  
  /**
   * 记录塔建造
   */
  recordTowerBuilt(count = 1) {
    this.statistics.totalTowersBuilt += count
    this.markDirty()
  }
  
  /**
   * 记录游戏时间
   */
  recordPlayTime(time) {
    this.statistics.totalPlayTime += time
    this.playerData.totalPlayTime += time
    this.markDirty()
  }
  
  // ==================== 数据导入导出 ====================
  
  /**
   * 导出所有数据
   */
  exportData() {
    return {
      playerData: this.playerData,
      levelProgress: this.levelProgress,
      settings: this.settings,
      statistics: this.statistics,
      exportTime: Date.now(),
      version: '1.0'
    }
  }
  
  /**
   * 导入数据
   */
  importData(data) {
    try {
      if (data.playerData) this.playerData = { ...this.defaultPlayerData, ...data.playerData }
      if (data.levelProgress) this.levelProgress = { ...this.defaultLevelProgress, ...data.levelProgress }
      if (data.settings) this.settings = { ...this.defaultSettings, ...data.settings }
      if (data.statistics) this.statistics = { ...this.defaultStatistics, ...data.statistics }
      
      this.markDirty()
      this.saveAllData()
      
      return true
    } catch (error) {
      console.error('数据导入失败:', error)
      return false
    }
  }
  
  /**
   * 重置所有数据
   */
  resetAllData() {
    this.playerData = { ...this.defaultPlayerData }
    this.levelProgress = { ...this.defaultLevelProgress }
    this.settings = { ...this.defaultSettings }
    this.statistics = { ...this.defaultStatistics }
    
    this.playerData.createdTime = Date.now()
    this.playerData.lastLoginTime = Date.now()
    
    this.markDirty()
    this.saveAllData()
  }
  
  /**
   * 清理资源
   */
  destroy() {
    this.stopAutoSave()
    
    // 最后保存一次
    if (this.isDirty) {
      this.saveAllData()
    }
  }
}

// 创建全局数据管理器实例
const dataManager = new DataManager()

// 导出模块
module.exports = dataManager
