/**
 * 微信小游戏屏幕适配器
 * 简化版本，直接使用屏幕尺寸，不进行复杂的适配变换
 */
class ScreenAdapter {
  constructor() {
    // 获取系统信息
    this.systemInfo = wx.getSystemInfoSync()

    // 屏幕基本信息
    this.screenWidth = this.systemInfo.screenWidth
    this.screenHeight = this.systemInfo.screenHeight
    this.windowWidth = this.systemInfo.windowWidth
    this.windowHeight = this.systemInfo.windowHeight
    this.pixelRatio = this.systemInfo.pixelRatio

    // 安全区域信息
    this.safeArea = this.systemInfo.safeArea || {
      left: 0,
      top: 0,
      right: this.windowWidth,
      bottom: this.windowHeight,
      width: this.windowWidth,
      height: this.windowHeight
    }

    // 状态栏高度
    this.statusBarHeight = this.systemInfo.statusBarHeight || 0

    // 设备方向
    this.deviceOrientation = this.systemInfo.deviceOrientation || 'portrait'

    // 简化的适配参数
    this.scale = 1
    this.offsetX = 0
    this.offsetY = 0
  }
  
  /**
   * 获取窗口宽度
   */
  getWindowWidth() {
    return this.windowWidth
  }

  /**
   * 获取窗口高度
   */
  getWindowHeight() {
    return this.windowHeight
  }

  /**
   * 获取适配比例（简化版本，始终返回1）
   */
  getScale() {
    return this.scale
  }

  /**
   * 获取X轴偏移（简化版本，始终返回0）
   */
  getOffsetX() {
    return this.offsetX
  }

  /**
   * 获取Y轴偏移（简化版本，始终返回0）
   */
  getOffsetY() {
    return this.offsetY
  }
  
  /**
   * 获取安全区域顶部位置
   */
  getSafeAreaTop() {
    return this.safeArea.top
  }
  
  /**
   * 获取安全区域底部位置
   */
  getSafeAreaBottom() {
    return this.safeArea.bottom
  }
  
  /**
   * 获取安全区域左侧位置
   */
  getSafeAreaLeft() {
    return this.safeArea.left
  }
  
  /**
   * 获取安全区域右侧位置
   */
  getSafeAreaRight() {
    return this.safeArea.right
  }
  
  /**
   * 获取安全区域宽度
   */
  getSafeAreaWidth() {
    return this.safeArea.width
  }
  
  /**
   * 获取安全区域高度
   */
  getSafeAreaHeight() {
    return this.safeArea.height
  }
  
  /**
   * 获取状态栏高度
   */
  getStatusBarHeight() {
    return this.statusBarHeight
  }
  
  /**
   * 获取设备像素比
   */
  getPixelRatio() {
    return this.pixelRatio
  }
  
  /**
   * 是否为横屏
   */
  isLandscape() {
    return this.deviceOrientation === 'landscape'
  }
  
  /**
   * 是否为竖屏
   */
  isPortrait() {
    return this.deviceOrientation === 'portrait'
  }
  
  /**
   * 获取Canvas的实际尺寸（考虑像素比）
   */
  getCanvasSize() {
    return {
      width: this.windowWidth * this.pixelRatio,
      height: this.windowHeight * this.pixelRatio
    }
  }
  
  /**
   * 设置Canvas尺寸和样式
   */
  setupCanvas(canvas) {
    const canvasSize = this.getCanvasSize()

    // 设置Canvas的实际尺寸（高清适配）
    canvas.width = canvasSize.width
    canvas.height = canvasSize.height

    // 设置Canvas的显示尺寸
    canvas.style = canvas.style || {}
    canvas.style.width = this.windowWidth + 'px'
    canvas.style.height = this.windowHeight + 'px'

    return canvas
  }
  
  /**
   * 转换触摸坐标到Canvas坐标
   */
  convertTouchToCanvas(touch) {
    // 考虑pixelRatio缩放的触摸坐标转换
    return {
      x: touch.pageX,
      y: touch.pageY
    }
  }

  /**
   * 转换坐标到屏幕坐标（简化版本，直接返回原坐标）
   */
  convertDesignToScreen(x, y) {
    return {
      x: x,
      y: y
    }
  }
  
  /**
   * 转换Canvas坐标到显示坐标
   */
  convertCanvasToDisplay(x, y) {
    return {
      x: x / this.pixelRatio,
      y: y / this.pixelRatio
    }
  }
}

// 导出模块
module.exports = ScreenAdapter
